server:
  port: 49011
  servlet:
    context-path: /

spring:
  application:
    name: file-transfer-demo
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.sqlite.JDBC
    url: ********************************************
    username: 
    password: 
    druid:
      # 初始连接数
      initial-size: 1
      # 最小连接池数量
      min-idle: 1
      # 最大连接池数量
      max-active: 10
      # 获取连接时最大等待时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_UUID
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 文件传输服务端配置
file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      swagger-enabled: false
      cors-enabled: true
      allowed-origins:
        - "*"

      # 存储桶配置
      buckets:
        default:
          name: "default"
          storage-path: ./data/file-transfer/files
          upload-rate-limit: 10485760  # 10MB/s
          download-rate-limit: 10485760  # 10MB/s
          default-chunk-size: 2097152  # 2MB
          max-file-size: 104857600  # 100MB
          max-in-memory-size: 10485760  # 10MB
          fast-upload-enabled: true
          rate-limit-enabled: true
        demo:
          name: "demo"
          storage-path: ./data/file-transfer/demo/files
          upload-rate-limit: 5242880   # 5MB/s
          download-rate-limit: 5242880  # 5MB/s
          default-chunk-size: 1048576  # 1MB
          max-file-size: 52428800      # 50MB
          max-in-memory-size: 10485760 # 10MB
          fast-upload-enabled: true
          rate-limit-enabled: true

      # 用户配置
      users:
        demo:
          secret-key: "demo-secret-key-2024"
          allowed-buckets: ["default", "demo"]
          upload-rate-limit: 5242880   # 5MB/s
          download-rate-limit: 5242880  # 5MB/s
          max-file-size: 52428800      # 50MB
          rate-limit-enabled: true

# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: DEBUG
    com.sdesrd.filetransfer.server.config.DatabaseInitializer: DEBUG
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# Knife4j配置
knife4j:
  enable: true
  setting:
    enable-version: true
    enable-swagger-models: true 