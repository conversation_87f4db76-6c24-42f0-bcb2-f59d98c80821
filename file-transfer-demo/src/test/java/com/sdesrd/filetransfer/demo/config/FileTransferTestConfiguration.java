package com.sdesrd.filetransfer.demo.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;

/**
 * 测试配置类
 * 解决Bean依赖冲突问题
 */
@TestConfiguration
public class FileTransferTestConfiguration {
    
    /**
     * 创建主要的FileTransferProperties Bean
     * 使用@Primary注解解决Bean冲突
     */
    @Bean
    @Primary
    public FileTransferProperties testFileTransferProperties() {
        System.out.println("[测试配置] 创建主要的FileTransferProperties Bean");
        
        FileTransferProperties properties = new FileTransferProperties();
        
        // 设置基本配置
        properties.setEnabled(true);
        properties.setDatabasePath(":memory:");
        
        // 创建默认存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName("default");
        bucketConfig.setStoragePath(System.getProperty("java.io.tmpdir") + "/file-transfer-test/test-user/files");
        bucketConfig.setUploadRateLimit(52428800L);
        bucketConfig.setDownloadRateLimit(52428800L);
        bucketConfig.setMaxFileSize(104857600L);
        bucketConfig.setDefaultChunkSize(512 * 1024); // 512KB
        bucketConfig.setMaxInMemorySize(10 * 1024 * 1024L); // 10MB
        bucketConfig.setFastUploadEnabled(true);
        bucketConfig.setRateLimitEnabled(false);
        
        properties.getBuckets().put("default", bucketConfig);
        
        // 设置用户配置
        FileTransferProperties.UserConfig userConfig = new FileTransferProperties.UserConfig();
        userConfig.setSecretKey("test-secret-key-2024");
        userConfig.setAllowedBuckets(new String[]{"default"});
        userConfig.setUploadRateLimit(52428800L);
        userConfig.setDownloadRateLimit(52428800L);
        userConfig.setMaxFileSize(104857600L);
        userConfig.setRateLimitEnabled(false);
        
        properties.getUsers().put("test-user", userConfig);
        
        return properties;
    }
}
