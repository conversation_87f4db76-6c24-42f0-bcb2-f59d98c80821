package com.sdesrd.filetransfer.demo.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;

/**
 * 测试配置类
 * 解决Bean依赖冲突问题
 */
@TestConfiguration
public class FileTransferTestConfiguration {
    
    /**
     * 创建主要的FileTransferProperties Bean
     * 使用@Primary注解解决Bean冲突
     */
    @Bean
    @Primary
    public FileTransferProperties testFileTransferProperties() {
        System.out.println("[测试配置] 创建主要的FileTransferProperties Bean");

        FileTransferProperties properties = new FileTransferProperties();

        // 设置基本配置
        properties.setEnabled(true);
        properties.setDatabasePath(":memory:");

        // 创建默认存储桶配置
        FileTransferProperties.BucketConfig defaultBucketConfig = new FileTransferProperties.BucketConfig();
        defaultBucketConfig.setName("default");
        defaultBucketConfig.setStoragePath(System.getProperty("java.io.tmpdir") + "/file-transfer-test/default/files");
        defaultBucketConfig.setUploadRateLimit(52428800L);
        defaultBucketConfig.setDownloadRateLimit(52428800L);
        defaultBucketConfig.setMaxFileSize(104857600L);
        defaultBucketConfig.setDefaultChunkSize(524288); // 512KB
        defaultBucketConfig.setMaxInMemorySize(10485760L); // 10MB
        defaultBucketConfig.setFastUploadEnabled(true);
        defaultBucketConfig.setRateLimitEnabled(false);

        properties.getBuckets().put("default", defaultBucketConfig);

        // 创建demo存储桶配置
        FileTransferProperties.BucketConfig demoBucketConfig = new FileTransferProperties.BucketConfig();
        demoBucketConfig.setName("demo");
        demoBucketConfig.setStoragePath(System.getProperty("java.io.tmpdir") + "/file-transfer-test/demo/files");
        demoBucketConfig.setUploadRateLimit(5242880L); // 5MB/s
        demoBucketConfig.setDownloadRateLimit(5242880L);
        demoBucketConfig.setMaxFileSize(52428800L); // 50MB
        demoBucketConfig.setDefaultChunkSize(1048576); // 1MB
        demoBucketConfig.setMaxInMemorySize(10485760L); // 10MB
        demoBucketConfig.setFastUploadEnabled(true);
        demoBucketConfig.setRateLimitEnabled(false);

        properties.getBuckets().put("demo", demoBucketConfig);

        // 设置test-user用户配置
        FileTransferProperties.UserConfig testUserConfig = new FileTransferProperties.UserConfig();
        testUserConfig.setSecretKey("test-secret-key-2024");
        testUserConfig.setAllowedBuckets(new String[]{"default"});
        testUserConfig.setUploadRateLimit(52428800L);
        testUserConfig.setDownloadRateLimit(52428800L);
        testUserConfig.setMaxFileSize(104857600L);
        testUserConfig.setRateLimitEnabled(false);

        properties.getUsers().put("test-user", testUserConfig);

        // 设置demo用户配置
        FileTransferProperties.UserConfig demoUserConfig = new FileTransferProperties.UserConfig();
        demoUserConfig.setSecretKey("demo-secret-key-2024");
        demoUserConfig.setAllowedBuckets(new String[]{"default", "demo"});
        demoUserConfig.setUploadRateLimit(5242880L);
        demoUserConfig.setDownloadRateLimit(5242880L);
        demoUserConfig.setMaxFileSize(52428800L);
        demoUserConfig.setRateLimitEnabled(false);

        properties.getUsers().put("demo", demoUserConfig);

        return properties;
    }
}
