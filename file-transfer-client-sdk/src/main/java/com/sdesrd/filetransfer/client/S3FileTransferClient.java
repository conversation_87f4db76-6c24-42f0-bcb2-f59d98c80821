package com.sdesrd.filetransfer.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.*;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.AuthUtils;
import com.sdesrd.filetransfer.client.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * S3风格的文件传输客户端
 * 提供类似AWS S3 SDK的操作接口
 */
@Slf4j
public class S3FileTransferClient implements Closeable {
    
    private final ClientConfig config;
    private final OkHttpClient httpClient;
    
    public S3FileTransferClient(ClientConfig config) {
        this.config = config;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeoutSeconds(), TimeUnit.SECONDS)
                .readTimeout(config.getReadTimeoutSeconds(), TimeUnit.SECONDS)
                .writeTimeout(config.getWriteTimeoutSeconds(), TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 上传对象（同步）
     */
    public S3PutObjectResult putObject(String key, String filePath, TransferListener listener) throws FileTransferException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileTransferException("文件不存在: " + filePath);
        }
        
        return putObject(key, file, listener);
    }
    
    /**
     * 上传对象（同步）
     */
    public S3PutObjectResult putObject(String key, File file, TransferListener listener) throws FileTransferException {
        try {
            // 创建上传请求
            S3PutObjectRequest request = new S3PutObjectRequest();
            request.setKey(key);
            request.setFileName(file.getName());
            request.setFileSize(file.length());
            request.setContentMd5(FileUtils.calculateMD5(file));
            request.setOverwrite(true); // 默认允许覆盖
            
            // 初始化上传
            FileUploadInitResponse initResponse = initS3Upload(request);
            String transferId = initResponse.getTransferId();
            
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setTransferId(transferId);
                progress.setFileName(file.getName());
                progress.setTotalSize(file.length());
                progress.setTransferredSize(0L);
                progress.setProgress(0.0);
                listener.onProgress(progress);
            }
            
            // 如果支持秒传
            if (Boolean.TRUE.equals(initResponse.getFastUpload())) {
                // 完成上传
                String result = completeS3Upload(transferId, key);
                
                S3PutObjectResult putResult = new S3PutObjectResult();
                putResult.setKey(key);
                putResult.setEtag(initResponse.getFileId());
                putResult.setSuccess(true);
                
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setTransferId(transferId);
                    progress.setFileName(file.getName());
                    progress.setTotalSize(file.length());
                    progress.setTransferredSize(file.length());
                    progress.setProgress(100.0);
                    listener.onCompleted(progress);
                }
                
                return putResult;
            }
            
            // 分块上传
            long chunkSize = initResponse.getChunkSize();
            int totalChunks = initResponse.getTotalChunks();
            
            try (FileInputStream fis = new FileInputStream(file)) {
                for (int i = 0; i < totalChunks; i++) {
                    byte[] chunkData = new byte[(int) Math.min(chunkSize, file.length() - i * chunkSize)];
                    int bytesRead = fis.read(chunkData);
                    
                    if (bytesRead > 0) {
                        // 如果读取的字节数小于数组长度，调整数组大小
                        if (bytesRead < chunkData.length) {
                            byte[] actualData = new byte[bytesRead];
                            System.arraycopy(chunkData, 0, actualData, 0, bytesRead);
                            chunkData = actualData;
                        }
                        
                        String chunkMd5 = FileUtils.calculateMD5(chunkData);
                        uploadChunk(transferId, i, chunkData, chunkMd5);
                        
                        // 更新进度
                        if (listener != null) {
                            long transferredSize = (long) (i + 1) * chunkSize;
                            if (transferredSize > file.length()) {
                                transferredSize = file.length();
                            }
                            
                            TransferProgress progress = new TransferProgress();
                            progress.setTransferId(transferId);
                            progress.setFileName(file.getName());
                            progress.setTotalSize(file.length());
                            progress.setTransferredSize(transferredSize);
                            progress.setProgress((double) transferredSize / file.length() * 100);
                            listener.onProgress(progress);
                        }
                    }
                }
            }
            
            // 完成上传
            String result = completeS3Upload(transferId, key);
            
            S3PutObjectResult putResult = new S3PutObjectResult();
            putResult.setKey(key);
            putResult.setEtag(transferId);
            putResult.setSuccess(true);
            
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setTransferId(transferId);
                progress.setFileName(file.getName());
                progress.setTotalSize(file.length());
                progress.setTransferredSize(file.length());
                progress.setProgress(100.0);
                listener.onCompleted(progress);
            }
            
            return putResult;
            
        } catch (Exception e) {
            log.error("上传对象失败 - key: {}, file: {}", key, file.getAbsolutePath(), e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(key);
                listener.onError(progress, e);
            }
            throw new FileTransferException("上传对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步上传对象
     */
    public CompletableFuture<S3PutObjectResult> putObjectAsync(String key, String filePath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return putObject(key, filePath, listener);
            } catch (FileTransferException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 获取对象（下载）
     */
    public S3GetObjectResult getObject(String key, String localPath, TransferListener listener) throws FileTransferException {
        try {
            log.info("下载对象 - key: {}, localPath: {}", key, localPath);
            
            String url = config.getServerUrl() + "/filetransfer/api/s3/" + key;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("下载失败: " + response.code() + " - " + errorBody);
                }
                
                ResponseBody body = response.body();
                if (body == null) {
                    throw new FileTransferException("响应体为空");
                }
                
                long totalSize = body.contentLength();
                
                // 确保目标目录存在
                File localFile = new File(localPath);
                File parentDir = localFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    parentDir.mkdirs();
                }
                
                // 下载文件
                try (InputStream inputStream = body.byteStream();
                     FileOutputStream outputStream = new FileOutputStream(localFile)) {
                    
                    byte[] buffer = new byte[8192];
                    long transferredSize = 0;
                    int bytesRead;
                    
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        transferredSize += bytesRead;
                        
                        // 更新进度
                        if (listener != null && totalSize > 0) {
                            TransferProgress progress = new TransferProgress();
                            progress.setFileName(key);
                            progress.setTotalSize(totalSize);
                            progress.setTransferredSize(transferredSize);
                            progress.setProgress((double) transferredSize / totalSize * 100);
                            listener.onProgress(progress);
                        }
                    }
                }
                
                S3GetObjectResult result = new S3GetObjectResult();
                result.setKey(key);
                result.setLocalPath(localPath);
                result.setSuccess(true);
                
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(key);
                    progress.setTotalSize(totalSize);
                    progress.setTransferredSize(totalSize);
                    progress.setProgress(100.0);
                    listener.onCompleted(progress);
                }
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("下载对象失败 - key: {}", key, e);
            if (listener != null) {
                TransferProgress progress = new TransferProgress();
                progress.setFileName(key);
                listener.onError(progress, e);
            }
            throw new FileTransferException("下载对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步获取对象
     */
    public CompletableFuture<S3GetObjectResult> getObjectAsync(String key, String localPath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getObject(key, localPath, listener);
            } catch (FileTransferException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 列出对象
     */
    public S3ListObjectsResult listObjects(String prefix, String delimiter, Integer maxKeys) throws FileTransferException {
        try {
            StringBuilder urlBuilder = new StringBuilder(config.getServerUrl() + "/filetransfer/api/s3/?");
            
            if (prefix != null) {
                urlBuilder.append("prefix=").append(java.net.URLEncoder.encode(prefix, "UTF-8")).append("&");
            }
            if (delimiter != null) {
                urlBuilder.append("delimiter=").append(java.net.URLEncoder.encode(delimiter, "UTF-8")).append("&");
            }
            if (maxKeys != null) {
                urlBuilder.append("max-keys=").append(maxKeys).append("&");
            }
            
            String url = urlBuilder.toString();
            if (url.endsWith("&")) {
                url = url.substring(0, url.length() - 1);
            }
            
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("列出对象失败: " + response.code() + " - " + errorBody);
                }
                
                String responseBody = response.body().string();
                ApiResult<S3ListObjectsResponse> apiResult = JSON.parseObject(responseBody, 
                        new TypeReference<ApiResult<S3ListObjectsResponse>>() {});
                
                if (!apiResult.isSuccess()) {
                    throw new FileTransferException("列出对象失败: " + apiResult.getMessage());
                }
                
                S3ListObjectsResult result = new S3ListObjectsResult();
                result.setListObjectsResponse(apiResult.getData());
                result.setSuccess(true);
                
                return result;
            }
            
        } catch (Exception e) {
            log.error("列出对象失败 - prefix: {}", prefix, e);
            throw new FileTransferException("列出对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除对象
     */
    public boolean deleteObject(String key) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/s3/" + key;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .delete();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    if (response.code() == 404) {
                        return false; // 文件不存在
                    }
                    throw new FileTransferException("删除对象失败: " + response.code() + " - " + errorBody);
                }
                
                return true;
            }
            
        } catch (Exception e) {
            log.error("删除对象失败 - key: {}", key, e);
            throw new FileTransferException("删除对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取对象信息
     */
    public S3ObjectInfo getObjectInfo(String key) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/filetransfer/api/s3/info/" + key;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            
            // 添加认证头
            AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    if (response.code() == 404) {
                        return null; // 文件不存在
                    }
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new FileTransferException("获取对象信息失败: " + response.code() + " - " + errorBody);
                }
                
                String responseBody = response.body().string();
                ApiResult<S3ObjectInfo> apiResult = JSON.parseObject(responseBody, 
                        new TypeReference<ApiResult<S3ObjectInfo>>() {});
                
                if (!apiResult.isSuccess()) {
                    throw new FileTransferException("获取对象信息失败: " + apiResult.getMessage());
                }
                
                return apiResult.getData();
            }
            
        } catch (Exception e) {
            log.error("获取对象信息失败 - key: {}", key, e);
            throw new FileTransferException("获取对象信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 初始化S3上传
     */
    private FileUploadInitResponse initS3Upload(S3PutObjectRequest request) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/init";
        String json = JSON.toJSONString(request);
        
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request httpRequest = requestBuilder.build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("初始化上传失败: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            ApiResult<FileUploadInitResponse> apiResult = JSON.parseObject(responseBody, 
                    new TypeReference<ApiResult<FileUploadInitResponse>>() {});
            
            if (!apiResult.isSuccess()) {
                throw new IOException("初始化上传失败: " + apiResult.getMessage());
            }
            
            return apiResult.getData();
        }
    }
    
    /**
     * 上传分块
     */
    private void uploadChunk(String transferId, int chunkIndex, byte[] chunkData, String chunkMd5) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/chunk";
        
        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("transferId", transferId)
                .addFormDataPart("chunkIndex", String.valueOf(chunkIndex))
                .addFormDataPart("chunkMd5", chunkMd5)
                .addFormDataPart("chunk", "chunk_" + chunkIndex, 
                        RequestBody.create(MediaType.parse("application/octet-stream"), chunkData))
                .build();
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody);
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("上传分块失败: " + response.code() + " - " + errorBody);
            }
        }
    }
    
    /**
     * 完成S3上传
     */
    private String completeS3Upload(String transferId, String key) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/s3/upload/complete?transferId=" + transferId + "&key=" + java.net.URLEncoder.encode(key, "UTF-8");
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json"), ""));
        
        // 添加认证头
        AuthUtils.addAuthHeaders(requestBuilder, config.getAuth());
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("完成上传失败: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            ApiResult<String> apiResult = JSON.parseObject(responseBody, 
                    new TypeReference<ApiResult<String>>() {});
            
            if (!apiResult.isSuccess()) {
                throw new IOException("完成上传失败: " + apiResult.getMessage());
            }
            
            return apiResult.getData();
        }
    }
    
    @Override
    public void close() throws IOException {
        // OkHttpClient会自动管理连接池，一般不需要手动关闭
        // 如果需要立即释放资源，可以关闭连接池
    }
} 