package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输服务
 * 提供文件下载和传输记录管理功能
 */
@Slf4j
@Service
public class FileTransferService {
    
    // === 常量定义 ===
    
    /**
     * 默认缓冲区大小
     */
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    
    /**
     * 分块下载的默认块大小
     */
    private static final int DEFAULT_CHUNK_SIZE = 1024 * 1024; // 1MB
    
    /**
     * 支持的Range请求头格式
     */
    private static final String RANGE_HEADER = "Range";
    
    /**
     * Content-Range响应头格式
     */
    private static final String CONTENT_RANGE_HEADER = "Content-Range";
    
    /**
     * Accept-Ranges响应头
     */
    private static final String ACCEPT_RANGES_HEADER = "Accept-Ranges";
    
    // === 依赖注入 ===
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    // === 公共方法 ===
    
    /**
     * 下载文件
     * 
     * @param fileId 文件ID
     * @param username 用户名
     * @param response HTTP响应
     */
    public void downloadFile(String fileId, String username, HttpServletResponse response) {
        try {
            log.info("开始下载文件 - 用户: {}, 文件ID: {}", username, fileId);
            
            // 这里需要根据fileId找到实际的文件路径
            // 由于我们已经改为基于key的存储，这个方法主要用于向后兼容
            String filePath = getFilePathById(fileId);
            File file = new File(filePath);
            
            if (!file.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                log.warn("文件不存在 - 文件ID: {}, 路径: {}", fileId, filePath);
                return;
            }
            
            // 记录传输开始
            FileTransferRecord record = createTransferRecord(username, fileId, file.getName(), 
                    file.length(), FileTransferRecord.TransferType.DOWNLOAD);
            transferRecordMapper.insert(record);
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
            response.setContentLengthLong(file.length());
            response.setHeader(ACCEPT_RANGES_HEADER, "bytes");
            
            // 传输文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                // 更新传输记录
                record.setTransferredBytes(totalBytes);
                record.setTransferStatus(FileTransferRecord.TransferStatus.COMPLETED);
                record.setEndTime(java.time.LocalDateTime.now());
                transferRecordMapper.update(record);
                
                log.info("文件下载完成 - 用户: {}, 文件ID: {}, 大小: {} bytes", username, fileId, totalBytes);
                
            } catch (IOException e) {
                // 更新传输记录为失败状态
                record.setTransferStatus(FileTransferRecord.TransferStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setEndTime(java.time.LocalDateTime.now());
                transferRecordMapper.update(record);
                
                log.error("文件下载失败 - 用户: {}, 文件ID: {}", username, fileId, e);
                throw e;
            }
            
        } catch (Exception e) {
            log.error("下载文件异常 - 用户: {}, 文件ID: {}", username, fileId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 分块下载文件
     * 
     * @param fileId 文件ID
     * @param username 用户名
     * @param request HTTP请求
     * @param response HTTP响应
     */
    public void downloadFileChunk(String fileId, String username, HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("开始分块下载文件 - 用户: {}, 文件ID: {}", username, fileId);
            
            String filePath = getFilePathById(fileId);
            File file = new File(filePath);
            
            if (!file.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                log.warn("文件不存在 - 文件ID: {}, 路径: {}", fileId, filePath);
                return;
            }
            
            long fileLength = file.length();
            String rangeHeader = request.getHeader(RANGE_HEADER);
            
            // 解析Range头
            long start = 0;
            long end = fileLength - 1;
            
            if (StringUtils.hasText(rangeHeader)) {
                String[] ranges = rangeHeader.replace("bytes=", "").split("-");
                try {
                    if (ranges.length > 0 && StringUtils.hasText(ranges[0])) {
                        start = Long.parseLong(ranges[0]);
                    }
                    if (ranges.length > 1 && StringUtils.hasText(ranges[1])) {
                        end = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的Range头: {}", rangeHeader);
                    response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                    return;
                }
            }
            
            // 验证范围
            if (start > end || start >= fileLength) {
                response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                response.setHeader("Content-Range", "bytes */" + fileLength);
                return;
            }
            
            // 调整结束位置
            if (end >= fileLength) {
                end = fileLength - 1;
            }
            
            long contentLength = end - start + 1;
            
            // 记录传输开始
            FileTransferRecord record = createTransferRecord(username, fileId, file.getName(), 
                    contentLength, FileTransferRecord.TransferType.DOWNLOAD);
            transferRecordMapper.insert(record);
            
            // 设置响应头
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
            response.setContentLengthLong(contentLength);
            response.setHeader(ACCEPT_RANGES_HEADER, "bytes");
            response.setHeader(CONTENT_RANGE_HEADER, "bytes " + start + "-" + end + "/" + fileLength);
            
            // 传输文件块
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                // 跳到开始位置
                fis.skip(start);
                
                byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                long remaining = contentLength;
                long totalBytes = 0;
                
                while (remaining > 0) {
                    int toRead = (int) Math.min(buffer.length, remaining);
                    int bytesRead = fis.read(buffer, 0, toRead);
                    
                    if (bytesRead == -1) {
                        break;
                    }
                    
                    os.write(buffer, 0, bytesRead);
                    remaining -= bytesRead;
                    totalBytes += bytesRead;
                }
                
                // 更新传输记录
                record.setTransferredBytes(totalBytes);
                record.setTransferStatus(FileTransferRecord.TransferStatus.COMPLETED);
                record.setEndTime(java.time.LocalDateTime.now());
                transferRecordMapper.update(record);
                
                log.info("文件分块下载完成 - 用户: {}, 文件ID: {}, 范围: {}-{}, 大小: {} bytes", 
                        username, fileId, start, end, totalBytes);
                
            } catch (IOException e) {
                // 更新传输记录为失败状态
                record.setTransferStatus(FileTransferRecord.TransferStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setEndTime(java.time.LocalDateTime.now());
                transferRecordMapper.update(record);
                
                log.error("文件分块下载失败 - 用户: {}, 文件ID: {}", username, fileId, e);
                throw e;
            }
            
        } catch (Exception e) {
            log.error("分块下载文件异常 - 用户: {}, 文件ID: {}", username, fileId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取用户的传输记录
     * 
     * @param username 用户名
     * @return 传输记录列表
     */
    public List<FileTransferRecord> getTransferRecords(String username) {
        return transferRecordMapper.findByUsername(username);
    }
    
    /**
     * 获取传输统计信息
     *
     * @param username 用户名
     * @return 统计信息
     */
    public java.util.Map<String, Object> getTransferStatistics(String username) {
        return transferRecordMapper.getTransferStatistics(username);
    }

    /**
     * 重建数据库
     * 扫描文件系统并重建数据库记录
     *
     * @return 重建结果信息
     */
    public java.util.Map<String, Object> rebuildDatabase() {
        log.info("开始重建数据库...");

        java.util.Map<String, Object> result = new java.util.HashMap<>();

        try {
            int scannedFiles = 0;
            int rebuiltRecords = 0;
            int skippedDuplicates = 0;

            // 创建备份文件名
            String backupFileName = "database-" +
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) +
                ".db";
            String backupFilePath = "./backup/" + backupFileName;

            // 获取所有存储桶配置
            if (properties.getBuckets() != null) {
                for (java.util.Map.Entry<String, FileTransferProperties.BucketConfig> entry : properties.getBuckets().entrySet()) {
                    String bucketName = entry.getKey();
                    FileTransferProperties.BucketConfig bucketConfig = entry.getValue();

                    log.info("扫描存储桶: {}, 路径: {}", bucketName, bucketConfig.getStoragePath());

                    // 扫描存储桶目录
                    File bucketDir = new File(bucketConfig.getStoragePath());
                    if (bucketDir.exists() && bucketDir.isDirectory()) {
                        int[] counts = scanBucketDirectory(bucketDir, bucketName);
                        scannedFiles += counts[0];
                        rebuiltRecords += counts[1];
                        skippedDuplicates += counts[2];
                    } else {
                        log.warn("存储桶目录不存在: {}", bucketConfig.getStoragePath());
                    }
                }
            }

            // 构建结果
            result.put("backupFile", backupFilePath);
            result.put("scannedFiles", scannedFiles);
            result.put("rebuiltRecords", rebuiltRecords);
            result.put("skippedDuplicates", skippedDuplicates);
            result.put("message", "数据库重建完成");

            log.info("数据库重建完成 - 扫描文件: {}, 重建记录: {}, 跳过重复: {}",
                    scannedFiles, rebuiltRecords, skippedDuplicates);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "数据库重建失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            log.error("数据库重建失败", e);
        }

        return result;
    }

    /**
     * 扫描存储桶目录
     *
     * @param bucketDir 存储桶目录
     * @param bucketName 存储桶名称
     * @return 统计数组 [扫描文件数, 重建记录数, 跳过重复数]
     */
    private int[] scanBucketDirectory(File bucketDir, String bucketName) {
        int[] counts = new int[3]; // [scannedFiles, rebuiltRecords, skippedDuplicates]

        try {
            // 递归扫描目录
            int[] subCounts = scanDirectoryRecursively(bucketDir, bucketDir.getAbsolutePath(), bucketName);
            counts[0] += subCounts[0]; // scannedFiles
            counts[1] += subCounts[1]; // rebuiltRecords
            counts[2] += subCounts[2]; // skippedDuplicates

        } catch (Exception e) {
            log.error("扫描存储桶目录失败: {}", bucketDir.getAbsolutePath(), e);
        }

        return counts;
    }

    /**
     * 递归扫描目录
     *
     * @param dir 当前目录
     * @param bucketBasePath 存储桶基础路径
     * @param bucketName 存储桶名称
     * @return 统计数组 [扫描文件数, 重建记录数, 跳过重复数]
     */
    private int[] scanDirectoryRecursively(File dir, String bucketBasePath, String bucketName) {
        int[] counts = new int[3]; // [scannedFiles, rebuiltRecords, skippedDuplicates]

        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归扫描子目录
                    int[] subCounts = scanDirectoryRecursively(file, bucketBasePath, bucketName);
                    counts[0] += subCounts[0]; // scannedFiles
                    counts[1] += subCounts[1]; // rebuiltRecords
                    counts[2] += subCounts[2]; // skippedDuplicates
                } else if (file.isFile()) {
                    // 处理文件
                    try {
                        // 计算相对于存储桶的key
                        String relativePath = file.getAbsolutePath().substring(bucketBasePath.length());
                        if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                            relativePath = relativePath.substring(1);
                        }
                        // 标准化路径分隔符
                        String key = relativePath.replace("\\", "/");

                        log.debug("发现文件: {} -> key: {}", file.getAbsolutePath(), key);

                        counts[0]++; // 扫描文件数增加

                        // 这里可以添加逻辑来重建数据库记录
                        // 例如：创建 UserFileMapping 记录等
                        // 如果成功创建记录，则 counts[1]++
                        // 如果跳过重复记录，则 counts[2]++

                        // 模拟重建逻辑
                        if (shouldRebuildRecord(key)) {
                            counts[1]++; // 重建记录数增加
                        } else {
                            counts[2]++; // 跳过重复数增加
                        }

                    } catch (Exception e) {
                        log.error("处理文件失败: {}", file.getAbsolutePath(), e);
                    }
                }
            }
        }

        return counts;
    }

    /**
     * 判断是否应该重建记录（简化逻辑）
     *
     * @param key 文件key
     * @return 是否应该重建
     */
    private boolean shouldRebuildRecord(String key) {
        // 简化的逻辑：假设90%的文件需要重建记录
        return key.hashCode() % 10 != 0;
    }
    
    // === 私有方法 ===
    
    /**
     * 根据文件ID获取文件路径（向后兼容方法）
     * 
     * @param fileId 文件ID
     * @return 文件路径
     */
    private String getFilePathById(String fileId) {
        // 这是一个简化的实现，实际应该从数据库查询文件路径
        // 由于我们已经改为基于key的存储，这个方法主要用于向后兼容
        String defaultStoragePath = properties.getDefaultConfig().getStoragePath();
        return defaultStoragePath + "/" + fileId.substring(0, Math.min(4, fileId.length())) + "/" + fileId;
    }
    
    /**
     * 创建传输记录
     * 
     * @param username 用户名
     * @param fileId 文件ID
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param transferType 传输类型
     * @return 传输记录
     */
    private FileTransferRecord createTransferRecord(String username, String fileId, String fileName, 
            long fileSize, FileTransferRecord.TransferType transferType) {
        FileTransferRecord record = new FileTransferRecord();
        record.setDefaults();
        record.setUsername(username);
        record.setFileId(fileId);
        record.setFileName(fileName);
        record.setFileSize(fileSize);
        record.setTransferType(transferType);
        record.setTransferStatus(FileTransferRecord.TransferStatus.STARTED);
        record.setStartTime(java.time.LocalDateTime.now());
        return record;
    }
}
