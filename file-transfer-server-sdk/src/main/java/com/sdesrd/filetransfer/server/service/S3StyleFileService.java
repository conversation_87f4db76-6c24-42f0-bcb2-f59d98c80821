package com.sdesrd.filetransfer.server.service;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.S3ListObjectsResponse;
import com.sdesrd.filetransfer.server.dto.S3ObjectInfo;
import com.sdesrd.filetransfer.server.dto.S3PutObjectRequest;
import com.sdesrd.filetransfer.server.dto.TransferProgressResponse;
import com.sdesrd.filetransfer.server.entity.UserFileMapping;
import com.sdesrd.filetransfer.server.entity.ObjectStorage;
import com.sdesrd.filetransfer.server.entity.TransferTask;
import com.sdesrd.filetransfer.server.entity.ChunkRecord;
import com.sdesrd.filetransfer.server.mapper.UserFileMappingMapper;
import com.sdesrd.filetransfer.server.mapper.ObjectStorageMapper;
import com.sdesrd.filetransfer.server.mapper.TransferTaskMapper;
import com.sdesrd.filetransfer.server.mapper.ChunkRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.service.FileTransferService;
import com.sdesrd.filetransfer.server.dto.DeleteObjectsRequest;

import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.Comparator;
import java.util.stream.Collectors;
import java.io.File;
import java.time.LocalDateTime;
import java.util.UUID;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

/**
 * S3风格的文件服务
 * 提供基于key的文件上传、下载、列表等操作
 */
@Slf4j
@Service
public class S3StyleFileService {

    // === 常量定义 ===

    /**
     * 默认路径分隔符
     */
    private static final String DEFAULT_DELIMITER = "/";

    /**
     * 默认最大返回对象数
     */
    private static final int DEFAULT_MAX_KEYS = 1000;

    /**
     * 路径分隔符
     */
    private static final String PATH_SEPARATOR = "/";

    /**
     * 文件ID最小长度（用于向后兼容的路径构建）
     */
    private static final int MIN_FILE_ID_LENGTH = 4;

    /**
     * 危险字符列表（用于路径安全验证）
     */
    private static final String[] DANGEROUS_CHARS = {"<", ">", ":", "\"", "|", "?", "*"};

    /**
     * 临时目录前缀
     */
    private static final String TEMP_DIR_PREFIX = "file-transfer";

    /**
     * 默认版本号
     */
    private static final int DEFAULT_VERSION = 1;

    /**
     * 默认内容类型
     */
    private static final String DEFAULT_CONTENT_TYPE = "application/octet-stream";

    /**
     * 默认存储类别
     */
    private static final String DEFAULT_STORAGE_CLASS = "STANDARD";

    // === 依赖注入 ===

    @Autowired
    private UserFileMappingMapper userFileMappingMapper;

    @Autowired
    private FileTransferService fileTransferService;

    @Autowired
    private FileTransferProperties properties;

    @Autowired
    private ObjectStorageMapper objectStorageMapper;

    @Autowired
    private TransferTaskMapper transferTaskMapper;

    @Autowired
    private ChunkRecordMapper chunkRecordMapper;
    
    /**
     * 初始化S3风格的文件上传
     */
    @Transactional
    public FileUploadInitResponse initS3Upload(S3PutObjectRequest request, String username, String clientIp) {
        try {
            log.info("初始化S3上传 - 用户: {}, key: {}, 文件: {}", username, request.getKey(), request.getFileName());
            
            // 验证key格式
            validateKey(request.getKey());
            
            // 检查是否覆盖现有文件
            if (!Boolean.TRUE.equals(request.getOverwrite())) {
                UserFileMapping existing = userFileMappingMapper.findByUserAndKey(username, request.getKey());
                if (existing != null) {
                    throw new RuntimeException("文件已存在: " + request.getKey() + "，请设置overwrite=true以覆盖");
                }
            }
            
            // 创建传统的上传请求
            FileUploadInitRequest traditionalRequest = new FileUploadInitRequest();
            traditionalRequest.setFileName(request.getFileName());
            traditionalRequest.setFileSize(request.getFileSize());
            traditionalRequest.setFileMd5(request.getContentMd5());
            if (request.getChunkSize() != null) {
                traditionalRequest.setChunkSize(request.getChunkSize().longValue());
            }
            
            // 调用原有的初始化上传逻辑
            FileUploadInitResponse response = fileTransferService.initUpload(traditionalRequest, username, clientIp);
            
            // 在响应中添加额外信息（通过扩展字段或注释记录key信息）
            // 注意：这里我们需要在完成上传时传递key信息
            
            return response;
            
        } catch (Exception e) {
            log.error("初始化S3上传失败 - 用户: {}, key: {}", username, request.getKey(), e);
            throw new RuntimeException("初始化上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 完成S3风格的文件上传
     */
    @Transactional
    public String completeS3Upload(String transferId, String fileKey, String username, String clientIp) {
        try {
            log.info("完成S3上传 - 用户: {}, transferId: {}, key: {}", username, transferId, fileKey);
            
            // 先完成传统的文件上传
            fileTransferService.completeUpload(transferId, username);
            
            // 获取上传完成的文件信息
            TransferProgressResponse progress = fileTransferService.queryProgress(transferId, username);
            if (progress == null || !progress.getCompleted()) {
                throw new RuntimeException("文件上传未完成");
            }
            
            // 如果覆盖现有文件，先将旧版本标记为非最新
            userFileMappingMapper.markAsNotLatest(username, fileKey);
            
            // 创建用户文件映射记录
            UserFileMapping mapping = new UserFileMapping();
            mapping.setId(UUID.randomUUID().toString());
            mapping.setUsername(username);
            mapping.setFileKey(fileKey);
            mapping.setFileId(transferId); // 使用transferId作为物理文件标识
            mapping.setFileName(progress.getFileName());
            mapping.setFileSize(progress.getTotalSize());
            mapping.setContentType(DEFAULT_CONTENT_TYPE); // 默认类型，可以后续优化
            mapping.setEtag(transferId); // 使用transferId作为ETag
            mapping.setVersion(getNextVersion(username, fileKey));
            mapping.setIsLatest(true);
            mapping.setCreateTime(LocalDateTime.now());
            mapping.setUpdateTime(LocalDateTime.now());
            
            userFileMappingMapper.insert(mapping);
            
            log.info("S3上传完成 - 用户: {}, key: {}, fileId: {}", username, fileKey, transferId);
            return "上传完成";
            
        } catch (Exception e) {
            log.error("完成S3上传失败 - transferId: {}, key: {}", transferId, fileKey, e);
            throw new RuntimeException("完成上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取S3对象信息
     */
    public S3ObjectInfo getS3ObjectInfo(String key, String username) {
        try {
            UserFileMapping mapping = userFileMappingMapper.findByUserAndKey(username, key);
            if (mapping == null) {
                return null;
            }
            
            return convertToS3ObjectInfo(mapping);
            
        } catch (Exception e) {
            log.error("获取S3对象信息失败 - 用户: {}, key: {}", username, key, e);
            throw new RuntimeException("获取对象信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 下载S3对象
     */
    public void downloadS3Object(String key, String username, HttpServletRequest request, HttpServletResponse response) {
        try {
            UserFileMapping mapping = userFileMappingMapper.findByUserAndKey(username, key);
            if (mapping == null) {
                throw new RuntimeException("文件不存在: " + key);
            }
            
            log.info("下载S3对象 - 用户: {}, key: {}, fileId: {}", username, key, mapping.getFileId());
            
            // 调用原有的下载逻辑
            fileTransferService.downloadFile(mapping.getFileId(), username, response);
            
        } catch (Exception e) {
            log.error("下载S3对象失败 - 用户: {}, key: {}", username, key, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"下载文件失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * 分块下载S3对象
     */
    public void downloadS3ObjectChunk(String key, String username, HttpServletRequest request, HttpServletResponse response) {
        try {
            UserFileMapping mapping = userFileMappingMapper.findByUserAndKey(username, key);
            if (mapping == null) {
                throw new RuntimeException("文件不存在: " + key);
            }
            
            log.info("分块下载S3对象 - 用户: {}, key: {}, fileId: {}", username, key, mapping.getFileId());
            
            // 调用原有的分块下载逻辑
            fileTransferService.downloadFileChunk(mapping.getFileId(), username, request, response);
            
        } catch (Exception e) {
            log.error("分块下载S3对象失败 - 用户: {}, key: {}", username, key, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"message\":\"分块下载文件失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * 列出S3对象
     */
    public S3ListObjectsResponse listS3Objects(String prefix, String delimiter, Integer maxKeys, 
                                              String continuationToken, String username) {
        try {
            if (prefix == null) {
                prefix = "";
            }
            if (delimiter == null) {
                delimiter = DEFAULT_DELIMITER;
            }
            if (maxKeys == null || maxKeys <= 0) {
                maxKeys = DEFAULT_MAX_KEYS;
            }
            
            log.info("列出S3对象 - 用户: {}, prefix: {}, delimiter: {}, maxKeys: {}", 
                    username, prefix, delimiter, maxKeys);
            
            S3ListObjectsResponse response = new S3ListObjectsResponse();
            response.setBucketName(username);
            response.setPrefix(prefix);
            response.setDelimiter(delimiter);
            response.setMaxKeys(maxKeys);
            response.setContinuationToken(continuationToken);
            
            // 查询文件映射
            List<UserFileMapping> mappings = userFileMappingMapper.findByUserAndPrefix(username, prefix);
            
            List<S3ObjectInfo> contents = new ArrayList<>();
            List<String> commonPrefixes = new ArrayList<>();
            
            for (UserFileMapping mapping : mappings) {
                String key = mapping.getFileKey();
                
                // 检查是否在当前前缀下
                if (!key.startsWith(prefix)) {
                    continue;
                }
                
                String remainingKey = key.substring(prefix.length());
                
                // 如果包含分隔符，则为"目录"
                if (remainingKey.contains(delimiter)) {
                    String dirPrefix = prefix + remainingKey.substring(0, remainingKey.indexOf(delimiter) + 1);
                    if (!commonPrefixes.contains(dirPrefix)) {
                        commonPrefixes.add(dirPrefix);
                    }
                } else {
                    // 直接文件
                    contents.add(convertToS3ObjectInfo(mapping));
                }
                
                // 检查是否超出maxKeys限制
                if (contents.size() + commonPrefixes.size() >= maxKeys) {
                    response.setIsTruncated(true);
                    break;
                }
            }
            
            response.setContents(contents);
            response.setCommonPrefixes(commonPrefixes);
            response.setKeyCount(contents.size());
            response.setIsTruncated(response.getIsTruncated() != null && response.getIsTruncated());
            
            return response;
            
        } catch (Exception e) {
            log.error("列出S3对象失败 - 用户: {}, prefix: {}", username, prefix, e);
            throw new RuntimeException("列出对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除S3对象
     */
    @Transactional
    public boolean deleteS3Object(String key, String username) {
        try {
            log.info("删除S3对象 - 用户: {}, key: {}", username, key);
            
            UserFileMapping mapping = userFileMappingMapper.findByUserAndKey(username, key);
            if (mapping == null) {
                log.warn("尝试删除不存在的对象 - 用户: {}, key: {}", username, key);
                return false;
            }
            
            // 删除用户文件映射
            int deleted = userFileMappingMapper.deleteByUserAndKey(username, key);
            
            // 检查物理文件是否还有其他引用
            int refCount = userFileMappingMapper.countFileReferences(mapping.getFileId());
            if (refCount == 0) {
                // 如果没有其他引用，可以删除物理文件
                // 这里可以添加物理文件删除逻辑
                log.info("物理文件 {} 没有其他引用，可以删除", mapping.getFileId());
            }
            
            log.info("S3对象删除完成 - 用户: {}, key: {}, deleted: {}", username, key, deleted > 0);
            return deleted > 0;
            
        } catch (Exception e) {
            log.error("删除S3对象失败 - 用户: {}, key: {}", username, key, e);
            throw new RuntimeException("删除对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证key格式
     */
    private void validateKey(String key) {
        if (!StringUtils.hasText(key)) {
            throw new IllegalArgumentException("文件key不能为空");
        }
        
        if (key.startsWith("/") || key.endsWith("/")) {
            throw new IllegalArgumentException("文件key不能以/开头或结尾");
        }
        
        if (key.contains("//")) {
            throw new IllegalArgumentException("文件key不能包含连续的//");
        }
        
        if (key.contains("..")) {
            throw new IllegalArgumentException("文件key不能包含..路径");
        }
        
        // 其他安全检查...
    }
    
    /**
     * 获取下一个版本号
     */
    private Integer getNextVersion(String username, String fileKey) {
        // 简单实现：每次都是版本1，如果需要版本控制可以查询最大版本号+1
        return DEFAULT_VERSION;
    }
    
    /**
     * 转换为S3ObjectInfo
     */
    private S3ObjectInfo convertToS3ObjectInfo(UserFileMapping mapping) {
        S3ObjectInfo info = new S3ObjectInfo();
        info.setKey(mapping.getFileKey());
        info.setSize(mapping.getFileSize());
        info.setEtag(mapping.getEtag());
        info.setContentType(mapping.getContentType());
        info.setVersion(mapping.getVersion());
        info.setIsLatest(mapping.getIsLatest());
        info.setOwner(mapping.getUsername());
        info.setStorageClass(DEFAULT_STORAGE_CLASS);
        info.setIsDirectory(false);
        info.setFormattedSize(FileUtils.formatFileSize(mapping.getFileSize()));
        
        // 解析时间
        try {
            info.setLastModified(Instant.parse(mapping.getUpdateTime() + "Z"));
            info.setCreatedTime(Instant.parse(mapping.getCreateTime() + "Z"));
        } catch (Exception e) {
            // 如果解析失败，使用当前时间
            info.setLastModified(Instant.now());
            info.setCreatedTime(Instant.now());
        }
        
        return info;
    }

    /**
     * 初始化上传
     */
    public FileUploadInitResponse initUpload(S3PutObjectRequest request, String username, String clientIp) {
        // 验证存储桶权限
        if (!properties.isUserAllowedToBucket(username, request.getBucket())) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + request.getBucket());
        }

        // 获取有效配置
        FileTransferProperties.EffectiveConfig config = properties.getEffectiveConfig(username, request.getBucket());

        // 检查文件大小限制
        if (request.getFileSize() > config.getMaxFileSize()) {
            throw new IllegalArgumentException("文件大小超出限制: " + request.getFileSize());
        }

        // 检查是否已存在相同对象
        ObjectStorage existingObject = objectStorageMapper.findByBucketAndKey(request.getBucket(), request.getKey());
        if (existingObject != null && !Boolean.TRUE.equals(request.getOverwrite())) {
            throw new IllegalArgumentException("对象已存在，设置overwrite=true以覆盖");
        }

        // 秒传检查
        if (config.isFastUploadEnabled() && request.getContentMd5() != null) {
            ObjectStorage existingFile = objectStorageMapper.findByMd5Hash(request.getContentMd5());
            if (existingFile != null) {
                // 创建新的对象映射
                ObjectStorage newObject = createObjectStorage(request, username, existingFile.getFileId());
                if (existingObject != null) {
                    newObject.setId(existingObject.getId());
                    objectStorageMapper.updateById(newObject);
                } else {
                    objectStorageMapper.insert(newObject);
                }

                FileUploadInitResponse response = new FileUploadInitResponse();
                response.setTransferId(TransferTask.generateTransferId());
                response.setFastUpload(true);
                response.setFileId(existingFile.getFileId());
                return response;
            }
        }

        // 创建传输任务
        TransferTask task = createTransferTask(request, username);
        transferTaskMapper.insert(task);

        FileUploadInitResponse response = new FileUploadInitResponse();
        response.setTransferId(task.getTransferId());
        response.setChunkSize(config.getDefaultChunkSize());
        response.setFastUpload(false);
        response.setTotalChunks(calculateTotalChunks(request.getFileSize(), config.getDefaultChunkSize()));

        return response;
    }

    /**
     * 上传分块
     */
    public void uploadChunk(String transferId, Integer chunkIndex, String chunkMd5, MultipartFile chunk, String username) {
        TransferTask task = transferTaskMapper.selectById(transferId);
        if (task == null || !task.getUserName().equals(username)) {
            throw new IllegalArgumentException("传输任务不存在或无权限");
        }

        // 验证分块MD5
        try {
            String actualMd5 = FileUtils.calculateMD5(chunk.getInputStream());
            if (!actualMd5.equals(chunkMd5)) {
                throw new IllegalArgumentException("分块MD5校验失败");
            }
        } catch (IOException e) {
            throw new RuntimeException("分块处理失败", e);
        }

        // 获取配置
        FileTransferProperties.EffectiveConfig config = properties.getEffectiveConfig(username, task.getBucket());

        // 限速处理
        if (config.isRateLimitEnabled()) {
            RateLimitUtils.limitRate(username + "_upload", config.getUploadRateLimit(), chunk.getSize());
        }

        // 保存分块文件
        String chunkPath = saveChunkFile(transferId, chunkIndex, chunk);

        // 记录分块信息
        ChunkRecord record = new ChunkRecord();
        record.setId(ChunkRecord.generateId());
        record.setTransferId(transferId);
        record.setChunkIndex(chunkIndex);
        record.setChunkSize((int) chunk.getSize());
        record.setChunkMd5(chunkMd5);
        record.setChunkPath(chunkPath);
        record.setStatus(ChunkRecord.Status.UPLOADED);
        record.setCreateTime(LocalDateTime.now());
        chunkRecordMapper.insert(record);

        // 更新任务进度
        int completedChunks = chunkRecordMapper.countCompletedChunks(transferId);
        transferTaskMapper.updateProgress(transferId, completedChunks, TransferTask.Status.UPLOADING);
    }

    /**
     * 完成上传
     */
    public Map<String, Object> completeUpload(String transferId, String bucket, String key, String username) {
        TransferTask task = transferTaskMapper.selectById(transferId);
        if (task == null || !task.getUserName().equals(username)) {
            throw new IllegalArgumentException("传输任务不存在或无权限");
        }

        // 验证所有分块都已上传
        int completedChunks = chunkRecordMapper.countCompletedChunks(transferId);
        if (completedChunks != task.getTotalChunks()) {
            throw new IllegalArgumentException("文件上传未完成");
        }

        // 合并分块
        String fileId = mergeChunks(task);

        // 创建对象存储记录
        ObjectStorage objectStorage = new ObjectStorage();
        objectStorage.setId(ObjectStorage.generateId());
        objectStorage.setBucket(bucket);
        objectStorage.setObjectKey(key);
        objectStorage.setFileId(fileId);
        objectStorage.setFileName(task.getFileName());
        objectStorage.setFileSize(task.getFileSize());
        objectStorage.setContentType(guessContentType(task.getFileName()));
        objectStorage.setMd5Hash(task.getFileMd5());
        objectStorage.setUserName(username);
        objectStorage.setCreateTime(LocalDateTime.now());
        objectStorage.setUpdateTime(LocalDateTime.now());

        // 检查是否已存在，如果存在则更新
        ObjectStorage existing = objectStorageMapper.findByBucketAndKey(bucket, key);
        if (existing != null) {
            objectStorage.setId(existing.getId());
            objectStorageMapper.updateById(objectStorage);
        } else {
            objectStorageMapper.insert(objectStorage);
        }

        // 完成传输任务
        transferTaskMapper.completeTransfer(transferId, TransferTask.Status.COMPLETED);

        // 清理分块记录
        chunkRecordMapper.deleteByTransferId(transferId);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("bucket", bucket);
        result.put("key", key);
        result.put("fileId", fileId);
        result.put("etag", task.getFileMd5());
        result.put("size", task.getFileSize());

        return result;
    }

    /**
     * 获取对象
     */
    public Resource getObject(String bucket, String key, String username) {
        // 验证权限
        if (!properties.isUserAllowedToBucket(username, bucket)) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + bucket);
        }

        ObjectStorage object = objectStorageMapper.findByBucketAndKey(bucket, key);
        if (object == null) {
            throw new IllegalArgumentException("对象不存在: " + key);
        }

        // 使用新的路径构建方式：基于bucket和key
        String filePath = getPhysicalFilePath(bucket, key);
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + filePath);
        }

        return new FileSystemResource(file);
    }

    /**
     * 获取对象信息
     */
    public S3ObjectInfo getObjectInfo(String bucket, String key, String username) {
        // 验证权限
        if (!properties.isUserAllowedToBucket(username, bucket)) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + bucket);
        }

        ObjectStorage object = objectStorageMapper.findByBucketAndKey(bucket, key);
        if (object == null) {
            throw new IllegalArgumentException("对象不存在: " + key);
        }

        return convertToS3ObjectInfo(object);
    }

    /**
     * 列出对象
     */
    public S3ListObjectsResponse listObjects(String bucket, String prefix, String delimiter, int maxKeys, String username) {
        // 验证权限
        if (!properties.isUserAllowedToBucket(username, bucket)) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + bucket);
        }

        String searchPrefix = prefix != null ? prefix + "%" : "%";
        List<ObjectStorage> objects = objectStorageMapper.listObjectsByPrefix(bucket, searchPrefix);

        S3ListObjectsResponse response = new S3ListObjectsResponse();
        response.setBucket(bucket);
        response.setPrefix(prefix);
        response.setDelimiter(delimiter);
        response.setMaxKeys(maxKeys);

        if (delimiter != null && !delimiter.isEmpty()) {
            // 处理目录结构
            Set<String> commonPrefixes = new HashSet<>();
            List<S3ObjectInfo> contents = new ArrayList<>();

            for (ObjectStorage obj : objects) {
                String key = obj.getObjectKey();
                if (prefix != null && !key.startsWith(prefix)) {
                    continue;
                }

                String relativePath = prefix != null ? key.substring(prefix.length()) : key;
                int delimiterIndex = relativePath.indexOf(delimiter);

                if (delimiterIndex > 0) {
                    // 这是一个"目录"
                    String commonPrefix = (prefix != null ? prefix : "") + relativePath.substring(0, delimiterIndex + 1);
                    commonPrefixes.add(commonPrefix);
                } else {
                    // 这是一个文件
                    contents.add(convertToS3ObjectInfo(obj));
                }
            }

            response.setCommonPrefixes(new ArrayList<>(commonPrefixes));
            response.setContents(contents);
        } else {
            // 简单列表
            List<S3ObjectInfo> contents = objects.stream()
                    .map(this::convertToS3ObjectInfo)
                    .collect(Collectors.toList());
            response.setContents(contents);
            response.setCommonPrefixes(new ArrayList<>());
        }

        return response;
    }

    /**
     * 删除对象
     */
    public void deleteObject(String bucket, String key, String username) {
        // 验证权限
        if (!properties.isUserAllowedToBucket(username, bucket)) {
            throw new IllegalArgumentException("用户无权访问存储桶: " + bucket);
        }

        ObjectStorage object = objectStorageMapper.findByBucketAndKey(bucket, key);
        if (object == null) {
            throw new IllegalArgumentException("对象不存在: " + key);
        }

        // 删除对象记录
        objectStorageMapper.deleteById(object.getId());

        // 检查是否还有其他对象引用同一个物理文件
        ObjectStorage sameFile = objectStorageMapper.findByMd5Hash(object.getMd5Hash());
        if (sameFile == null) {
            // 没有其他引用，删除物理文件
            // 使用新的路径构建方式：基于bucket和key
            String filePath = getPhysicalFilePath(bucket, key);
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("物理文件删除成功: {}", filePath);
                } else {
                    log.warn("物理文件删除失败: {}", filePath);
                }

                // 也尝试删除父目录（如果为空）
                File parentDir = file.getParentFile();
                if (parentDir != null && parentDir.exists()) {
                    String[] files = parentDir.list();
                    if (files != null && files.length == 0) {
                        boolean parentDeleted = parentDir.delete();
                        if (parentDeleted) {
                            log.info("空父目录删除成功: {}", parentDir.getAbsolutePath());
                        }
                    }
                }
            } else {
                log.warn("物理文件不存在，无需删除: {}", filePath);
            }
        }
    }

    /**
     * 批量删除对象
     */
    public Map<String, Object> deleteObjects(DeleteObjectsRequest request, String username) {
        int deletedCount = 0;
        List<String> failedKeys = new ArrayList<>();

        for (String key : request.getKeys()) {
            try {
                deleteObject(request.getBucket(), key, username);
                deletedCount++;
            } catch (Exception e) {
                log.warn("删除对象失败: bucket={}, key={}", request.getBucket(), key, e);
                failedKeys.add(key);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("deletedCount", deletedCount);
        result.put("failedKeys", failedKeys);
        result.put("totalRequested", request.getKeys().size());

        return result;
    }

    /**
     * 获取传输进度
     */
    public TransferProgressResponse getTransferProgress(String transferId, String username) {
        TransferTask task = transferTaskMapper.selectById(transferId);
        if (task == null || !task.getUserName().equals(username)) {
            throw new IllegalArgumentException("传输任务不存在或无权限");
        }

        TransferProgressResponse response = new TransferProgressResponse();
        response.setTransferId(transferId);
        response.setFileName(task.getFileName());
        response.setFileSize(task.getFileSize());
        response.setCompletedChunks(task.getCompletedChunks());
        response.setTotalChunks(task.getTotalChunks());
        response.setStatus(task.getStatus());
        response.setProgress(task.getTotalChunks() > 0 ? (double) task.getCompletedChunks() / task.getTotalChunks() * 100 : 0);

        return response;
    }

    // === 私有辅助方法 ===

    private ObjectStorage createObjectStorage(S3PutObjectRequest request, String username, String fileId) {
        ObjectStorage objectStorage = new ObjectStorage();
        objectStorage.setId(ObjectStorage.generateId());
        objectStorage.setBucket(request.getBucket());
        objectStorage.setObjectKey(request.getKey());
        objectStorage.setFileId(fileId);
        objectStorage.setFileName(request.getFileName());
        objectStorage.setFileSize(request.getFileSize());
        objectStorage.setContentType(request.getContentType());
        objectStorage.setMd5Hash(request.getContentMd5());
        objectStorage.setUserName(username);
        objectStorage.setCreateTime(LocalDateTime.now());
        objectStorage.setUpdateTime(LocalDateTime.now());
        return objectStorage;
    }

    private TransferTask createTransferTask(S3PutObjectRequest request, String username) {
        TransferTask task = new TransferTask();
        task.setTransferId(TransferTask.generateTransferId());
        task.setBucket(request.getBucket());
        task.setObjectKey(request.getKey());
        task.setFileName(request.getFileName());
        task.setFileSize(request.getFileSize());
        task.setFileMd5(request.getContentMd5());
        task.setUserName(username);
        
        FileTransferProperties.EffectiveConfig config = properties.getEffectiveConfig(username, request.getBucket());
        task.setChunkSize(config.getDefaultChunkSize());
        task.setTotalChunks(calculateTotalChunks(request.getFileSize(), config.getDefaultChunkSize()));
        task.setCompletedChunks(0);
        task.setStatus(TransferTask.Status.INITIALIZED);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        
        return task;
    }

    private int calculateTotalChunks(long fileSize, int chunkSize) {
        return (int) Math.ceil((double) fileSize / chunkSize);
    }

    private String saveChunkFile(String transferId, Integer chunkIndex, MultipartFile chunk) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir") + "/file-transfer/" + transferId;
            File dir = new File(tempDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String chunkPath = tempDir + "/chunk_" + chunkIndex;
            chunk.transferTo(new File(chunkPath));
            
            return chunkPath;
        } catch (IOException e) {
            throw new RuntimeException("保存分块文件失败", e);
        }
    }

    /**
     * 合并分块文件到最终位置
     * 使用新的存储路径格式：${bucket.storage-path}/{key}
     *
     * @param task 传输任务
     * @return 文件ID（MD5）
     */
    private String mergeChunks(TransferTask task) {
        try {
            String fileId = task.getFileMd5(); // 使用MD5作为文件ID

            // 使用新的路径构建方式：基于bucket和key
            String filePath = getPhysicalFilePath(task.getBucket(), task.getObjectKey());

            File targetFile = new File(filePath);
            // 确保父目录存在
            if (!targetFile.getParentFile().exists()) {
                boolean created = targetFile.getParentFile().mkdirs();
                if (!created) {
                    throw new RuntimeException("无法创建目标目录: " + targetFile.getParentFile().getAbsolutePath());
                }
            }

            // 获取所有分块
            List<ChunkRecord> chunks = chunkRecordMapper.findByTransferId(task.getTransferId());
            chunks.sort(Comparator.comparing(ChunkRecord::getChunkIndex));

            // 合并分块
            FileUtils.mergeChunks(chunks.stream().map(ChunkRecord::getChunkPath).collect(Collectors.toList()), filePath);

            // 清理临时分块文件
            for (ChunkRecord chunk : chunks) {
                File chunkFile = new File(chunk.getChunkPath());
                if (chunkFile.exists() && !chunkFile.delete()) {
                    log.warn("无法删除临时分块文件: {}", chunk.getChunkPath());
                }
            }

            // 清理临时目录
            String tempDir = System.getProperty("java.io.tmpdir") + PATH_SEPARATOR + TEMP_DIR_PREFIX + PATH_SEPARATOR + task.getTransferId();
            File tempDirFile = new File(tempDir);
            if (tempDirFile.exists() && !tempDirFile.delete()) {
                log.warn("无法删除临时目录: {}", tempDir);
            }

            log.info("文件合并完成 - 存储桶: {}, key: {}, 路径: {}", task.getBucket(), task.getObjectKey(), filePath);
            return fileId;
        } catch (Exception e) {
            log.error("合并分块失败 - 存储桶: {}, key: {}", task.getBucket(), task.getObjectKey(), e);
            throw new RuntimeException("合并分块失败", e);
        }
    }

    /**
     * 获取物理文件路径（基于key的新存储结构）
     * 新的存储路径格式：${bucket.storage-path}/{key}
     *
     * @param bucket 存储桶名称
     * @param key 对象key
     * @return 物理文件路径
     */
    private String getPhysicalFilePath(String bucket, String key) {
        // 获取存储桶配置
        FileTransferProperties.BucketConfig bucketConfig = properties.getBucketConfig(bucket);
        if (bucketConfig == null) {
            throw new IllegalArgumentException("存储桶不存在: " + bucket);
        }

        // 验证key的安全性
        validateKeyForPath(key);

        // 构建新的存储路径：${bucket.storage-path}/{key}
        return bucketConfig.getStoragePath() + PATH_SEPARATOR + key;
    }

    /**
     * 验证key用于路径构建的安全性
     *
     * @param key 对象key
     * @throws IllegalArgumentException 如果key包含不安全的字符
     */
    private void validateKeyForPath(String key) {
        if (!StringUtils.hasText(key)) {
            throw new IllegalArgumentException("对象key不能为空");
        }

        // 检查路径遍历攻击
        if (key.contains("..")) {
            throw new IllegalArgumentException("对象key不能包含..路径");
        }

        // 检查绝对路径
        if (key.startsWith("/") || key.startsWith("\\")) {
            throw new IllegalArgumentException("对象key不能以路径分隔符开头");
        }

        // 检查Windows驱动器路径
        if (key.matches("^[A-Za-z]:.*")) {
            throw new IllegalArgumentException("对象key不能包含驱动器路径");
        }

        // 检查其他危险字符
        for (String dangerousChar : DANGEROUS_CHARS) {
            if (key.contains(dangerousChar)) {
                throw new IllegalArgumentException("对象key不能包含危险字符: " + dangerousChar);
            }
        }
    }

    private String guessContentType(String fileName) {
        if (fileName == null) return "application/octet-stream";
        
        String ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (ext) {
            case "txt": return "text/plain";
            case "pdf": return "application/pdf";
            case "jpg": case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "mp4": return "video/mp4";
            case "zip": return "application/zip";
            default: return "application/octet-stream";
        }
    }

    private S3ObjectInfo convertToS3ObjectInfo(ObjectStorage object) {
        S3ObjectInfo info = new S3ObjectInfo();
        info.setKey(object.getObjectKey());
        info.setFileName(object.getFileName());
        info.setSize(object.getFileSize());
        info.setContentType(object.getContentType());
        info.setEtag(object.getMd5Hash());
        info.setLastModified(object.getUpdateTime());
        info.setFormattedSize(FileUtils.formatFileSize(object.getFileSize()));
        return info;
    }
} 