package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.FileTransferProperties.UserConfig;

/**
 * 认证服务测试
 */
@DisplayName("认证服务测试")
class AuthServiceTest {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceTest.class);

    // 测试日志标识常量
    private static final String EXPECTED_EXCEPTION_PREFIX = "[EXPECTED_EXCEPTION_TEST]";
    private static final String TEST_INFO_PREFIX = "[TEST_INFO]";

    private AuthService authService;
    private FileTransferProperties properties;

    private final String testUser = "test-user";
    private final String testSecretKey = "test-secret-key-2024";
    
    @BeforeEach
    void setUp() throws Exception {
        // 创建配置
        properties = new FileTransferProperties();
        properties.setEnabled(true);
        
        // 配置测试存储桶
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName("test-bucket");
        bucketConfig.setStoragePath("./test-storage");
        bucketConfig.setUploadRateLimit(10485760L); // 10MB/s
        bucketConfig.setDownloadRateLimit(10485760L);
        bucketConfig.setDefaultChunkSize(1048576); // 1MB
        bucketConfig.setMaxFileSize(104857600L); // 100MB
        bucketConfig.setMaxInMemorySize(10485760L); // 10MB
        bucketConfig.setFastUploadEnabled(true);
        bucketConfig.setRateLimitEnabled(false);

        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put("test-bucket", bucketConfig);
        properties.setBuckets(buckets);

        // 配置测试用户（只包含用户级别的覆盖设置）
        UserConfig userConfig = new UserConfig();
        userConfig.setSecretKey(testSecretKey);
        userConfig.setAllowedBuckets(new String[]{"test-bucket"});

        Map<String, UserConfig> users = new HashMap<>();
        users.put(testUser, userConfig);
        properties.setUsers(users);
        
        // 创建认证服务
        authService = new AuthService();
        
        // 使用反射设置properties字段
        java.lang.reflect.Field propertiesField = AuthService.class.getDeclaredField("properties");
        propertiesField.setAccessible(true);
        propertiesField.set(authService, properties);
    }
    
    @Test
    @DisplayName("用户认证令牌生成测试")
    void testGenerateAuthToken() {
        // 生成认证令牌
        String authToken = authService.generateAuthToken(testUser, testSecretKey);
        
        // 验证令牌不为空
        assertNotNull(authToken);
        assertFalse(authToken.trim().isEmpty());
        
        // 验证令牌格式（应该包含时间戳和签名）
        assertTrue(authToken.contains(":"));
        String[] parts = authToken.split(":");
        assertEquals(3, parts.length); // user:timestamp:signature
        assertEquals(testUser, parts[0]);
        
        // 验证时间戳是数字
        assertDoesNotThrow(() -> {
            Long.parseLong(parts[1]);
        });
        
        // 验证签名不为空
        assertNotNull(parts[2]);
        assertFalse(parts[2].trim().isEmpty());
    }
    
    @Test
    @DisplayName("用户认证验证测试")
    void testAuthenticate() {
        // 生成有效的认证令牌
        String validToken = authService.generateAuthToken(testUser, testSecretKey);
        
        // 验证有效令牌
        assertTrue(authService.authenticate(testUser, validToken));
        
        // 验证无效用户名
        assertFalse(authService.authenticate("invalid-user", validToken));
        
        // 验证无效令牌
        assertFalse(authService.authenticate(testUser, "invalid-token"));
        
        // 验证空令牌
        assertFalse(authService.authenticate(testUser, ""));
        assertFalse(authService.authenticate(testUser, null));
        
        // 验证空用户名
        assertFalse(authService.authenticate("", validToken));
        assertFalse(authService.authenticate(null, validToken));
    }
    
    @Test
    @DisplayName("用户配置获取测试")
    void testGetUserConfig() {
        // 获取存在的用户配置
        UserConfig config = authService.getUserConfig(testUser);
        assertNotNull(config);
        assertEquals(testSecretKey, config.getSecretKey());

        // 获取有效配置（合并用户和存储桶配置）
        FileTransferProperties.EffectiveConfig effectiveConfig = properties.getEffectiveConfig(testUser, "test-bucket");
        assertNotNull(effectiveConfig);
        assertEquals(1048576, effectiveConfig.getDefaultChunkSize());
        assertEquals(104857600L, effectiveConfig.getMaxFileSize());
        assertTrue(effectiveConfig.isFastUploadEnabled());
        assertFalse(effectiveConfig.isRateLimitEnabled());

        // 获取不存在的用户配置（应该返回null）
        UserConfig defaultConfig = authService.getUserConfig("nonexistent-user");
        assertNull(defaultConfig);
    }
    
    @Test
    @DisplayName("HMAC签名计算测试")
    void testHmacSignature() throws Exception {
        String data = "test-data-for-hmac";
        String key = "test-secret-key";
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("calculateHmacSha256", String.class, String.class);
        method.setAccessible(true);
        
        String signature1 = (String) method.invoke(authService, data, key);
        String signature2 = (String) method.invoke(authService, data, key);
        
        // 验证签名一致性
        assertNotNull(signature1);
        assertNotNull(signature2);
        assertEquals(signature1, signature2);
        
        // 验证不同数据产生不同签名
        String signature3 = (String) method.invoke(authService, "different-data", key);
        assertNotEquals(signature1, signature3);
        
        // 验证不同密钥产生不同签名
        String signature4 = (String) method.invoke(authService, data, "different-key");
        assertNotEquals(signature1, signature4);
    }
    
    @Test
    @DisplayName("令牌过期测试")
    void testTokenExpiration() throws Exception {
        log.info("{} 开始测试令牌过期场景 - 这将产生预期的过期警告日志", EXPECTED_EXCEPTION_PREFIX);

        // 创建一个过期的令牌（使用过去的时间戳）
        final long twoHoursInMillis = 2L * 60L * 60L * 1000L; // 2小时的毫秒数
        long expiredTimestamp = System.currentTimeMillis() - twoHoursInMillis;

        // 使用反射创建过期令牌
        java.lang.reflect.Method method = AuthService.class.getDeclaredMethod("calculateHmacSha256", String.class, String.class);
        method.setAccessible(true);

        String data = testUser + ":" + expiredTimestamp;
        String signature = (String) method.invoke(authService, data, testSecretKey);
        String expiredToken = testUser + ":" + expiredTimestamp + ":" + signature;

        log.info("{} 测试过期令牌认证 - 预期结果：认证失败并记录过期警告", EXPECTED_EXCEPTION_PREFIX);

        // 验证过期令牌被拒绝
        assertFalse(authService.authenticate(testUser, expiredToken));

        log.info("{} 令牌过期测试完成 - 过期令牌已被正确拒绝", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("令牌格式验证测试")
    void testTokenFormatValidation() {
        log.info("{} 开始测试无效令牌格式 - 这将产生预期的格式错误和NumberFormatException", EXPECTED_EXCEPTION_PREFIX);

        // 测试格式错误的令牌
        String[] invalidTokens = {
            "invalid-format",
            "user:timestamp", // 缺少签名
            "user:invalid-timestamp:signature", // 时间戳格式错误
            ":timestamp:signature", // 缺少用户名
            "user::signature", // 缺少时间戳
            "user:timestamp:", // 缺少签名
            "user:timestamp:signature:extra" // 多余的部分
        };

        for (String invalidToken : invalidTokens) {
            log.info("{} 测试无效令牌格式: {} - 预期结果：认证失败", EXPECTED_EXCEPTION_PREFIX, invalidToken);

            assertFalse(authService.authenticate(testUser, invalidToken),
                    "无效令牌应该被拒绝: " + invalidToken);
        }

        log.info("{} 令牌格式验证测试完成 - 所有无效令牌均被正确拒绝", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("默认配置测试")
    void testDefaultConfig() {
        // 创建没有用户配置的properties，但有默认存储桶
        FileTransferProperties emptyProperties = new FileTransferProperties();
        emptyProperties.setEnabled(true);
        emptyProperties.setUsers(new HashMap<>());

        // 添加默认存储桶配置
        FileTransferProperties.BucketConfig defaultBucket = new FileTransferProperties.BucketConfig();
        defaultBucket.setName("default");
        defaultBucket.setStoragePath("./default-storage");
        defaultBucket.setDefaultChunkSize(2097152); // 2MB
        defaultBucket.setMaxFileSize(104857600L); // 100MB
        defaultBucket.setMaxInMemorySize(10485760L); // 10MB
        defaultBucket.setUploadRateLimit(10485760L); // 10MB/s
        defaultBucket.setDownloadRateLimit(10485760L); // 10MB/s
        defaultBucket.setFastUploadEnabled(true);
        defaultBucket.setRateLimitEnabled(true);

        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put("default", defaultBucket);
        emptyProperties.setBuckets(buckets);

        AuthService emptyAuthService = new AuthService();
        try {
            java.lang.reflect.Field propertiesField = AuthService.class.getDeclaredField("properties");
            propertiesField.setAccessible(true);
            propertiesField.set(emptyAuthService, emptyProperties);
        } catch (Exception e) {
            fail("设置properties失败: " + e.getMessage());
        }

        // 获取不存在用户的配置（应该返回null）
        UserConfig defaultConfig = emptyAuthService.getUserConfig("any-user");
        assertNull(defaultConfig);

        // 获取默认存储桶配置
        FileTransferProperties.BucketConfig defaultBucketConfig = emptyProperties.getDefaultConfig();
        assertNotNull(defaultBucketConfig);
        assertEquals(2097152, defaultBucketConfig.getDefaultChunkSize()); // 2MB
        assertEquals(104857600L, defaultBucketConfig.getMaxFileSize()); // 100MB
        assertEquals(10485760L, defaultBucketConfig.getMaxInMemorySize()); // 10MB
        assertEquals(10485760L, defaultBucketConfig.getUploadRateLimit()); // 10MB/s
        assertEquals(10485760L, defaultBucketConfig.getDownloadRateLimit()); // 10MB/s
        assertTrue(defaultBucketConfig.isFastUploadEnabled());
        assertTrue(defaultBucketConfig.isRateLimitEnabled());
    }
    
    @Test
    @DisplayName("并发认证测试")
    void testConcurrentAuthentication() throws InterruptedException {
        final int threadCount = 10;
        final int operationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        final boolean[] results = new boolean[threadCount];
        
        // 创建多个线程同时进行认证
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                boolean allSuccess = true;
                for (int j = 0; j < operationsPerThread; j++) {
                    String token = authService.generateAuthToken(testUser, testSecretKey);
                    if (!authService.authenticate(testUser, token)) {
                        allSuccess = false;
                        break;
                    }
                }
                results[threadIndex] = allSuccess;
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有线程都成功
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 认证失败");
        }
    }
}
