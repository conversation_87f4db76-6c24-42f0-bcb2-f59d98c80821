package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.FileTransferProperties.UserConfig;
import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.util.FileUtils;

/**
 * 文件传输服务集成测试
 * 注意：这是一个简化的集成测试，主要测试业务逻辑而不是Spring容器
 */
@DisplayName("文件传输服务集成测试")
class FileTransferServiceIntegrationTest {
    
    @TempDir
    Path tempDir;
    
    private FileTransferService fileTransferService;
    private AuthService authService;
    private FileTransferProperties properties;
    
    private final String testUser = "test-user";
    private final String testSecretKey = "test-secret-key-2024";
    
    @BeforeEach
    void setUp() {
        // 创建配置
        properties = new FileTransferProperties();
        properties.setEnabled(true);
        properties.setDatabasePath(tempDir.resolve("test-database.db").toString());

        // 配置测试存储桶
        FileTransferProperties.BucketConfig bucketConfig = new FileTransferProperties.BucketConfig();
        bucketConfig.setName("test-bucket");
        bucketConfig.setStoragePath(tempDir.resolve("storage").toString());
        bucketConfig.setUploadRateLimit(10485760L); // 10MB/s
        bucketConfig.setDownloadRateLimit(10485760L);
        bucketConfig.setDefaultChunkSize(1048576); // 1MB
        bucketConfig.setMaxFileSize(104857600L); // 100MB
        bucketConfig.setMaxInMemorySize(10485760L); // 10MB
        bucketConfig.setFastUploadEnabled(true);
        bucketConfig.setRateLimitEnabled(false); // 测试时不启用限速

        Map<String, FileTransferProperties.BucketConfig> buckets = new HashMap<>();
        buckets.put("test-bucket", bucketConfig);
        properties.setBuckets(buckets);

        // 配置测试用户（只包含用户级别的覆盖设置）
        UserConfig userConfig = new UserConfig();
        userConfig.setSecretKey(testSecretKey);
        userConfig.setAllowedBuckets(new String[]{"test-bucket"});
        // 用户级别的配置可以覆盖存储桶配置
        userConfig.setUploadRateLimit(5242880L); // 5MB/s，覆盖存储桶设置
        userConfig.setMaxFileSize(52428800L); // 50MB，覆盖存储桶设置

        Map<String, UserConfig> users = new HashMap<>();
        users.put(testUser, userConfig);
        properties.setUsers(users);

        // 创建服务实例
        authService = new AuthService();
        // 使用反射设置properties字段，因为AuthService使用@Autowired注入
        try {
            java.lang.reflect.Field propertiesField = AuthService.class.getDeclaredField("properties");
            propertiesField.setAccessible(true);
            propertiesField.set(authService, properties);
        } catch (Exception e) {
            throw new RuntimeException("无法设置AuthService的properties字段", e);
        }

        // 注意：在实际测试中，需要正确初始化 FileTransferService 的所有依赖
        // 这里简化处理，实际使用时应该使用 @Autowired 注入
    }
    
    @Test
    @DisplayName("用户认证测试")
    void testUserAuthentication() {
        // 生成认证令牌
        String authToken = authService.generateAuthToken(testUser, testSecretKey);
        assertNotNull(authToken);
        
        // 验证认证
        boolean isAuthenticated = authService.authenticate(testUser, authToken);
        assertTrue(isAuthenticated);
        
        // 测试错误的用户名
        boolean wrongUser = authService.authenticate("wrong-user", authToken);
        assertFalse(wrongUser);
        
        // 测试错误的令牌
        boolean wrongToken = authService.authenticate(testUser, "wrong-token");
        assertFalse(wrongToken);
    }
    
    @Test
    @DisplayName("用户配置获取测试")
    void testGetUserConfig() {
        // 获取存在的用户配置
        UserConfig config = authService.getUserConfig(testUser);
        assertNotNull(config);
        assertEquals(testSecretKey, config.getSecretKey());

        // 获取有效配置（合并用户和存储桶配置）
        FileTransferProperties.EffectiveConfig effectiveConfig = properties.getEffectiveConfig(testUser, "test-bucket");
        assertNotNull(effectiveConfig);
        assertEquals(1048576, effectiveConfig.getDefaultChunkSize());

        // 获取不存在的用户配置（应该返回null）
        UserConfig defaultConfig = authService.getUserConfig("nonexistent-user");
        assertNull(defaultConfig);
    }
    
    @Test
    @DisplayName("文件上传初始化测试")
    void testFileUploadInit() throws IOException {
        // 创建测试文件
        File testFile = createTestFile("test-upload.txt", "测试文件内容");
        String fileMd5 = FileUtils.calculateFileMD5(testFile);
        
        // 创建上传初始化请求
        FileUploadInitRequest request = new FileUploadInitRequest();
        request.setFileName("test-upload.txt");
        request.setFileSize(testFile.length());
        request.setFileMd5(fileMd5);
        request.setChunkSize(1024L); // 1KB 分块
        
        // 注意：这里需要实际的 FileTransferService 实例
        // 在真实测试中，应该通过 @Autowired 注入或者完整的 Spring 上下文
        
        // 模拟测试逻辑
        assertNotNull(request.getFileName());
        assertNotNull(request.getFileMd5());
        assertTrue(request.getFileSize() > 0);
        
        // 验证分块计算
        long expectedChunks = (long) Math.ceil((double) request.getFileSize() / request.getChunkSize());
        assertTrue(expectedChunks > 0);
    }
    
    @Test
    @DisplayName("文件MD5计算测试")
    void testFileMD5Calculation() throws IOException {
        // 创建测试文件
        File testFile = createTestFile("md5-test.txt", "Hello, World!");
        
        // 计算MD5
        String md5 = FileUtils.calculateFileMD5(testFile);
        assertNotNull(md5);
        assertEquals(32, md5.length()); // MD5 应该是32位十六进制字符串
        
        // 验证相同内容的文件有相同的MD5
        File testFile2 = createTestFile("md5-test2.txt", "Hello, World!");
        String md5_2 = FileUtils.calculateFileMD5(testFile2);
        assertEquals(md5, md5_2);
        
        // 验证不同内容的文件有不同的MD5
        File testFile3 = createTestFile("md5-test3.txt", "Hello, World! Different");
        String md5_3 = FileUtils.calculateFileMD5(testFile3);
        assertNotEquals(md5, md5_3);
    }
    
    @Test
    @DisplayName("文件大小限制测试")
    void testFileSizeLimit() {
        // 获取有效配置（合并用户和存储桶配置）
        FileTransferProperties.EffectiveConfig effectiveConfig = properties.getEffectiveConfig(testUser, "test-bucket");

        // 测试正常大小的文件
        long normalSize = 1024 * 1024; // 1MB
        assertTrue(normalSize <= effectiveConfig.getMaxFileSize());

        // 测试超出限制的文件
        long oversizeFile = effectiveConfig.getMaxFileSize() + 1;
        assertTrue(oversizeFile > effectiveConfig.getMaxFileSize());
    }
    
    @Test
    @DisplayName("分块大小验证测试")
    void testChunkSizeValidation() {
        // 获取有效配置（合并用户和存储桶配置）
        FileTransferProperties.EffectiveConfig effectiveConfig = properties.getEffectiveConfig(testUser, "test-bucket");

        // 测试正常分块大小
        long normalChunkSize = 512 * 1024; // 512KB
        assertTrue(normalChunkSize <= effectiveConfig.getMaxInMemorySize());

        // 测试超出内存限制的分块
        long oversizeChunk = effectiveConfig.getMaxInMemorySize() + 1;
        assertTrue(oversizeChunk > effectiveConfig.getMaxInMemorySize());
    }
    
    @Test
    @DisplayName("文件路径安全性测试")
    void testFilePathSecurity() {
        // 测试路径遍历攻击
        String[] maliciousPaths = {
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM"
        };
        
        for (String maliciousPath : maliciousPaths) {
            // 在实际实现中，应该验证文件路径的安全性
            // 确保文件只能存储在指定的用户目录下
            assertFalse(isPathSafe(maliciousPath), "路径不安全: " + maliciousPath);
        }
        
        // 测试安全的路径
        String[] safePaths = {
            "normal-file.txt",
            "folder/file.txt",
            "user-data.json"
        };
        
        for (String safePath : safePaths) {
            assertTrue(isPathSafe(safePath), "路径应该安全: " + safePath);
        }
    }
    
    @Test
    @DisplayName("并发上传测试")
    void testConcurrentUploads() throws Exception {
        // 创建多个测试文件
        File[] testFiles = new File[5];
        for (int i = 0; i < testFiles.length; i++) {
            // 创建重复内容（Java 8兼容方式）
            StringBuilder content = new StringBuilder();
            String baseContent = "并发测试文件内容 " + i + "\n";
            for (int j = 0; j < 100; j++) {
                content.append(baseContent);
            }
            testFiles[i] = createTestFile("concurrent-test-" + i + ".txt", content.toString());
        }
        
        // 模拟并发上传
        // 在实际测试中，这里应该使用多线程来测试并发场景
        for (File testFile : testFiles) {
            String md5 = FileUtils.calculateFileMD5(testFile);
            assertNotNull(md5);
            assertTrue(testFile.exists());
        }
        
        // 验证所有文件都有不同的MD5（因为内容不同）
        String[] md5Values = new String[testFiles.length];
        for (int i = 0; i < testFiles.length; i++) {
            md5Values[i] = FileUtils.calculateFileMD5(testFiles[i]);
        }
        
        // 检查MD5值的唯一性
        for (int i = 0; i < md5Values.length; i++) {
            for (int j = i + 1; j < md5Values.length; j++) {
                assertNotEquals(md5Values[i], md5Values[j], 
                        "文件 " + i + " 和文件 " + j + " 的MD5不应该相同");
            }
        }
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes("UTF-8"));
        return file;
    }
    
    /**
     * 检查文件路径是否安全
     * 简化的安全检查逻辑
     */
    private boolean isPathSafe(String path) {
        // 检查路径遍历攻击
        if (path.contains("..") || path.contains("\\..") || path.contains("../")) {
            return false;
        }
        
        // 检查绝对路径
        if (path.startsWith("/") || path.matches("^[A-Za-z]:.*")) {
            return false;
        }
        
        // 检查系统敏感路径
        String lowerPath = path.toLowerCase();
        if (lowerPath.contains("etc/passwd") || lowerPath.contains("system32") || 
            lowerPath.contains("windows") || lowerPath.contains("config")) {
            return false;
        }
        
        return true;
    }

    @Test
    @DisplayName("新存储规则路径生成测试")
    void testNewStoragePathRule() {
        // 测试新的存储路径规则：${storage-path}/{key}
        String bucketPath = "/data/buckets/documents";
        String key = "reports/2024/annual.pdf";

        // 构建预期路径（新的简化存储结构）
        String expectedPath = bucketPath + "/" + key;
        String actualPath = buildNewFilePathByRule(bucketPath, key);

        assertEquals(expectedPath, actualPath);
        assertEquals("/data/buckets/documents/reports/2024/annual.pdf", actualPath);
    }

    @Test
    @DisplayName("旧存储规则路径生成测试（向后兼容）")
    void testLegacyStoragePathRule() {
        // 测试旧的存储路径规则：${storage-path}/fileId前4位/fileId/实体文件名
        String fileId = "abcd1234567890ef";
        String fileName = "test-document.pdf";
        String storagePath = "/data/files";

        // 构建预期路径（旧的多层目录结构）
        String expectedPath = storagePath + "/" + fileId.substring(0, 4) + "/" + fileId + "/" + fileName;
        String actualPath = buildLegacyFilePathByRule(storagePath, fileId, fileName);

        assertEquals(expectedPath, actualPath);
        assertEquals("/data/files/abcd/abcd1234567890ef/test-document.pdf", actualPath);
    }

    @Test
    @DisplayName("路径安全性验证测试")
    void testPathSecurityValidation() {
        String bucketPath = "/data/buckets/documents";

        // 测试正常的key
        String validKey = "reports/2024/annual.pdf";
        assertDoesNotThrow(() -> buildNewFilePathByRule(bucketPath, validKey));

        // 测试包含路径遍历的key
        String traversalKey = "../../../etc/passwd";
        // 注意：这里只是测试路径构建，实际的安全验证应该在服务层进行
        String result = buildNewFilePathByRule(bucketPath, traversalKey);
        assertEquals("/data/buckets/documents/../../../etc/passwd", result);

        // 测试空key
        assertThrows(IllegalArgumentException.class, () -> buildNewFilePathByRule(bucketPath, ""));
        assertThrows(IllegalArgumentException.class, () -> buildNewFilePathByRule(bucketPath, null));

        // 测试包含特殊字符的key
        String specialCharKey = "reports/file<name>.pdf";
        String specialResult = buildNewFilePathByRule(bucketPath, specialCharKey);
        assertEquals("/data/buckets/documents/reports/file<name>.pdf", specialResult);
    }
    
    @Test
    @DisplayName("文件路径安全验证增强测试")
    void testEnhancedPathSecurity() {
        // 测试相对路径安全性
        String[] safePaths = {
            "documents/report.pdf",
            "images/photo.jpg",
            "data/export.csv",
            "folder1/folder2/file.txt"
        };
        
        for (String path : safePaths) {
            assertTrue(isRelativePathSafe(path), "相对路径应该安全: " + path);
        }
        
        // 测试不安全的相对路径
        String[] unsafePaths = {
            "../config/database.conf",
            "../../etc/passwd",
            "folder/../../../sensitive.txt",
            "./../system.log",
            "documents/../../../etc/shadow"
        };
        
        for (String path : unsafePaths) {
            assertFalse(isRelativePathSafe(path), "相对路径应该不安全: " + path);
        }
    }
    
    @Test
    @DisplayName("通过相对路径查找文件测试")
    void testFindFileByRelativePath() throws IOException {
        // 创建模拟的文件结构
        String fileId = "test1234567890ab";
        String relativePath = "documents/test-file.pdf";
        String fileName = "test-file.pdf";
        
        // 创建预期的存储路径结构
        File storageDir = tempDir.resolve("storage").toFile();
        File fileIdDir = new File(storageDir, fileId.substring(0, 4) + "/" + fileId);
        fileIdDir.mkdirs();
        
        File actualFile = new File(fileIdDir, fileName);
        Files.write(actualFile.toPath(), "测试文件内容".getBytes("UTF-8"));
        
        // 模拟在这个路径下查找文件
        assertTrue(actualFile.exists());
        assertEquals(fileName, actualFile.getName());
        
        // 验证路径结构符合新规则
        String expectedPattern = "storage/test/" + fileId + "/" + fileName;
        assertTrue(actualFile.getPath().contains("test/" + fileId + "/" + fileName));
    }
    
    @Test
    @DisplayName("数据库故障容错机制测试")
    void testDatabaseFaultTolerance() {
        // 模拟数据库查询失败的情况
        String fileId = "fault1234567890";
        String fileName = "fault-test.txt";
        
        // 测试通过文件系统路径规则查找文件的逻辑
        String storagePath = tempDir.resolve("storage").toString();
        String expectedPath = buildLegacyFilePathByRule(storagePath, fileId, fileName);
        
        assertNotNull(expectedPath);
        assertTrue(expectedPath.contains(fileId.substring(0, 4)));
        assertTrue(expectedPath.contains(fileId));
        assertTrue(expectedPath.contains(fileName));
    }
    
    @Test
    @DisplayName("文件MD5查找与容错测试")
    void testFindFileByMd5WithFallback() throws IOException {
        // 创建测试文件
        File testFile = createTestFile("md5-fallback-test.txt", "MD5容错测试内容");
        String fileMd5 = FileUtils.calculateFileMD5(testFile);
        
        // 验证MD5计算正确
        assertNotNull(fileMd5);
        assertEquals(32, fileMd5.length());
        
        // 模拟通过MD5查找文件的逻辑
        // 实际实现中，这里会先查询数据库，失败后通过文件系统查找
        boolean foundByMd5 = fileMd5 != null && fileMd5.length() == 32;
        assertTrue(foundByMd5);
    }
    
    @Test
    @DisplayName("数据库重建扫描逻辑测试")
    void testDatabaseRebuildScanLogic() throws IOException {
        // 创建模拟的文件存储结构
        File storageDir = tempDir.resolve("storage").toFile();
        storageDir.mkdirs();
        
        // 创建多个文件按新规则存储
        String[] fileIds = {"abcd1234567890", "efgh0987654321", "ijkl1122334455"};
        String[] fileNames = {"doc1.txt", "image.jpg", "data.csv"};
        
        for (int i = 0; i < fileIds.length; i++) {
            String fileId = fileIds[i];
            String fileName = fileNames[i];
            
            // 按新规则创建目录结构
            File fileIdDir = new File(storageDir, fileId.substring(0, 4) + "/" + fileId);
            fileIdDir.mkdirs();
            
            File file = new File(fileIdDir, fileName);
            Files.write(file.toPath(), ("测试文件内容 " + i).getBytes("UTF-8"));
        }
        
        // 扫描存储目录
        File[] firstLevelDirs = storageDir.listFiles(File::isDirectory);
        assertNotNull(firstLevelDirs);
        assertTrue(firstLevelDirs.length > 0);
        
        // 验证目录结构符合规则
        for (File firstLevel : firstLevelDirs) {
            assertEquals(4, firstLevel.getName().length()); // 前4位目录
            
            File[] fileIdDirs = firstLevel.listFiles(File::isDirectory);
            if (fileIdDirs != null) {
                for (File fileIdDir : fileIdDirs) {
                    assertTrue(fileIdDir.getName().startsWith(firstLevel.getName())); // fileId以前4位开头
                    
                    File[] files = fileIdDir.listFiles(File::isFile);
                    if (files != null && files.length > 0) {
                        // 验证文件存在
                        assertTrue(files[0].exists());
                        assertTrue(files[0].length() > 0);
                    }
                }
            }
        }
    }
    
    @Test
    @DisplayName("相对路径解析和验证测试")
    void testRelativePathParsing() {
        // 测试各种相对路径格式
        String[] validPaths = {
            "file.txt",
            "folder/file.txt",
            "deep/nested/folder/file.pdf",
            "中文目录/中文文件.docx",
            "folder-with-dash/file_with_underscore.txt"
        };
        
        for (String path : validPaths) {
            assertTrue(isRelativePathSafe(path), "路径应该有效: " + path);
            assertFalse(path.startsWith("/"), "不应该是绝对路径: " + path);
        }
        
        // 测试路径标准化
        String normalizedPath = normalizeRelativePath("folder//double-slash///file.txt");
        assertEquals("folder/double-slash/file.txt", normalizedPath);
    }
    
    /**
     * 根据新的存储规则构建文件路径
     * 新的存储路径格式：${bucket.storage-path}/{key}
     */
    private String buildNewFilePathByRule(String bucketPath, String key) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("对象key不能为空");
        }
        return bucketPath + "/" + key;
    }

    /**
     * 根据旧的存储规则构建文件路径（向后兼容）
     * 旧的存储路径格式：${storage-path}/${fileId前4位}/${fileId}/${fileName}
     */
    private String buildLegacyFilePathByRule(String storagePath, String fileId, String fileName) {
        if (fileId == null || fileId.length() < 4) {
            throw new IllegalArgumentException("文件ID长度不足");
        }
        return storagePath + "/" + fileId.substring(0, 4) + "/" + fileId + "/" + fileName;
    }
    
    /**
     * 检查相对路径是否安全
     */
    private boolean isRelativePathSafe(String relativePath) {
        if (relativePath == null || relativePath.trim().isEmpty()) {
            return false;
        }
        
        // 检查路径遍历攻击
        if (relativePath.contains("..")) {
            return false;
        }
        
        // 检查绝对路径
        if (relativePath.startsWith("/") || relativePath.startsWith("\\") || 
            relativePath.matches("^[A-Za-z]:.*")) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 标准化相对路径
     */
    private String normalizeRelativePath(String path) {
        if (path == null) {
            return null;
        }
        
        // 将多个连续的斜杠替换为单个斜杠
        return path.replaceAll("/+", "/");
    }

}
