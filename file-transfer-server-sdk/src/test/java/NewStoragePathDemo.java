import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * 新存储路径逻辑演示程序
 * 展示从旧的多层目录结构到新的基于key的单层结构的改进
 */
public class NewStoragePathDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 文件传输SDK存储路径逻辑演示 ===\n");
        
        try {
            // 创建临时演示目录
            Path tempDir = Files.createTempDirectory("storage-demo");
            System.out.println("演示环境: " + tempDir.toAbsolutePath());
            System.out.println();
            
            // 演示1：旧的存储路径结构
            demonstrateOldStorageStructure(tempDir);
            
            // 演示2：新的存储路径结构
            demonstrateNewStorageStructure(tempDir);
            
            // 演示3：路径对比分析
            compareStorageStructures(tempDir);
            
            // 演示4：实际文件操作
            demonstrateFileOperations(tempDir);
            
            // 演示5：多存储桶支持
            demonstrateMultipleBuckets(tempDir);
            
            System.out.println("\n=== 演示完成 ===");
            System.out.println("✅ 新的存储路径逻辑工作正常！");
            System.out.println("✅ 路径结构更加直观和易于管理！");
            System.out.println("✅ 支持多存储桶和复杂的目录结构！");
            
            // 清理演示目录
            deleteDirectory(tempDir.toFile());
            
        } catch (Exception e) {
            System.err.println("❌ 演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示旧的存储路径结构
     */
    private static void demonstrateOldStorageStructure(Path tempDir) throws IOException {
        System.out.println("=== 演示1：旧的存储路径结构 ===");
        
        String storagePath = tempDir.resolve("old-storage").toString();
        
        // 模拟旧的文件存储
        String[] fileIds = {"abcd1234567890ef", "1234abcdef567890", "5678efgh90123456"};
        String[] fileNames = {"document.pdf", "image.jpg", "data.csv"};
        
        for (int i = 0; i < fileIds.length; i++) {
            String fileId = fileIds[i];
            String fileName = fileNames[i];
            
            // 旧的路径格式：${storage-path}/${fileId前4位}/${fileId}/${fileName}
            String oldPath = storagePath + "/" + fileId.substring(0, 4) + "/" + fileId + "/" + fileName;
            
            // 创建目录结构
            File file = new File(oldPath);
            file.getParentFile().mkdirs();
            Files.write(file.toPath(), ("旧格式文件内容 " + i).getBytes("UTF-8"));
            
            System.out.println("旧路径: " + oldPath);
            System.out.println("  层级: " + (oldPath.split("/").length - storagePath.split("/").length) + " 层");
            System.out.println("  可读性: 较差（需要知道fileId才能找到文件）");
        }
        
        System.out.println("\n旧结构特点:");
        System.out.println("  ❌ 路径复杂，包含多层嵌套");
        System.out.println("  ❌ 依赖fileId，不直观");
        System.out.println("  ❌ 目录结构不反映文件的逻辑组织");
        System.out.println();
    }
    
    /**
     * 演示新的存储路径结构
     */
    private static void demonstrateNewStorageStructure(Path tempDir) throws IOException {
        System.out.println("=== 演示2：新的存储路径结构 ===");
        
        String bucketPath = tempDir.resolve("new-storage").resolve("documents").toString();
        
        // 模拟新的文件存储
        String[] keys = {
            "reports/2024/annual-report.pdf",
            "images/photos/vacation.jpg",
            "data/exports/user-data.csv",
            "documents/contracts/contract-2024.pdf",
            "backup/database/backup-20241201.sql"
        };
        
        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            
            // 新的路径格式：${bucket.storage-path}/{key}
            String newPath = bucketPath + "/" + key;
            
            // 创建目录结构
            File file = new File(newPath);
            file.getParentFile().mkdirs();
            Files.write(file.toPath(), ("新格式文件内容 " + i).getBytes("UTF-8"));
            
            System.out.println("新路径: " + newPath);
            System.out.println("  层级: " + (newPath.split("/").length - bucketPath.split("/").length) + " 层");
            System.out.println("  可读性: 优秀（路径直接反映文件组织结构）");
        }
        
        System.out.println("\n新结构特点:");
        System.out.println("  ✅ 路径直观，反映文件的逻辑组织");
        System.out.println("  ✅ 支持任意深度的目录结构");
        System.out.println("  ✅ 便于文件管理和查找");
        System.out.println("  ✅ 支持多存储桶隔离");
        System.out.println();
    }
    
    /**
     * 对比存储结构
     */
    private static void compareStorageStructures(Path tempDir) {
        System.out.println("=== 演示3：路径对比分析 ===");
        
        String fileId = "abcd1234567890ef";
        String fileName = "annual-report.pdf";
        String key = "reports/2024/annual-report.pdf";
        
        String oldPath = tempDir + "/old-storage/" + fileId.substring(0, 4) + "/" + fileId + "/" + fileName;
        String newPath = tempDir + "/new-storage/documents/" + key;
        
        System.out.println("相同文件的不同存储方式:");
        System.out.println();
        System.out.println("旧路径: " + oldPath);
        System.out.println("新路径: " + newPath);
        System.out.println();
        
        System.out.println("对比分析:");
        System.out.println("┌─────────────────┬─────────────────┬─────────────────┐");
        System.out.println("│      特性       │      旧结构     │      新结构     │");
        System.out.println("├─────────────────┼─────────────────┼─────────────────┤");
        System.out.println("│   路径可读性    │       较差      │       优秀      │");
        System.out.println("│   目录层级      │     固定3层     │     灵活可变    │");
        System.out.println("│   文件查找      │   需要fileId    │   直接通过key   │");
        System.out.println("│   逻辑组织      │      不支持     │     完全支持    │");
        System.out.println("│   多存储桶      │      复杂       │     原生支持    │");
        System.out.println("│   维护成本      │       高        │       低        │");
        System.out.println("└─────────────────┴─────────────────┴─────────────────┘");
        System.out.println();
    }
    
    /**
     * 演示实际文件操作
     */
    private static void demonstrateFileOperations(Path tempDir) throws IOException {
        System.out.println("=== 演示4：实际文件操作 ===");
        
        String bucketPath = tempDir.resolve("demo-bucket").toString();
        
        // 创建各种类型的文件
        List<String> testKeys = Arrays.asList(
            "用户文档/张三/个人简历.pdf",
            "项目文件/2024/项目计划.docx",
            "图片资源/产品图/产品A.jpg",
            "数据备份/2024-12/用户数据.json",
            "系统日志/应用日志/app-20241201.log"
        );
        
        System.out.println("创建测试文件:");
        for (String key : testKeys) {
            String filePath = bucketPath + "/" + key;
            File file = new File(filePath);
            
            // 创建父目录
            file.getParentFile().mkdirs();
            
            // 写入文件内容
            String content = "这是文件 " + key + " 的内容\n创建时间: " + java.time.LocalDateTime.now();
            Files.write(file.toPath(), content.getBytes("UTF-8"));
            
            System.out.println("  ✓ " + key + " (" + file.length() + " bytes)");
        }
        
        System.out.println("\n目录结构:");
        printDirectoryTree(new File(bucketPath), "");
        
        System.out.println("\n文件操作验证:");
        for (String key : testKeys) {
            String filePath = bucketPath + "/" + key;
            File file = new File(filePath);
            
            if (file.exists()) {
                String content = new String(Files.readAllBytes(file.toPath()), "UTF-8");
                System.out.println("  ✓ 读取 " + key + " 成功");
            } else {
                System.out.println("  ❌ 文件不存在: " + key);
            }
        }
        System.out.println();
    }
    
    /**
     * 演示多存储桶支持
     */
    private static void demonstrateMultipleBuckets(Path tempDir) throws IOException {
        System.out.println("=== 演示5：多存储桶支持 ===");
        
        String[] buckets = {"documents", "images", "backups"};
        String[][] bucketFiles = {
            {"reports/annual.pdf", "contracts/contract1.pdf"},
            {"photos/vacation.jpg", "avatars/user1.png"},
            {"database/backup.sql", "logs/system.log"}
        };
        
        for (int i = 0; i < buckets.length; i++) {
            String bucket = buckets[i];
            String bucketPath = tempDir.resolve("multi-bucket").resolve(bucket).toString();
            
            System.out.println("存储桶: " + bucket);
            System.out.println("路径: " + bucketPath);
            
            for (String key : bucketFiles[i]) {
                String filePath = bucketPath + "/" + key;
                File file = new File(filePath);
                file.getParentFile().mkdirs();
                
                String content = "存储桶 " + bucket + " 中的文件: " + key;
                Files.write(file.toPath(), content.getBytes("UTF-8"));
                
                System.out.println("  ✓ " + key);
            }
            System.out.println();
        }
        
        System.out.println("多存储桶优势:");
        System.out.println("  ✅ 不同类型文件完全隔离");
        System.out.println("  ✅ 可以设置不同的访问权限");
        System.out.println("  ✅ 便于备份和管理");
        System.out.println("  ✅ 支持不同的存储策略");
        System.out.println();
    }
    
    /**
     * 打印目录树结构
     */
    private static void printDirectoryTree(File dir, String prefix) {
        if (!dir.exists() || !dir.isDirectory()) {
            return;
        }
        
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }
        
        Arrays.sort(files, (a, b) -> {
            if (a.isDirectory() && !b.isDirectory()) return -1;
            if (!a.isDirectory() && b.isDirectory()) return 1;
            return a.getName().compareTo(b.getName());
        });
        
        for (int i = 0; i < files.length; i++) {
            File file = files[i];
            boolean isLast = (i == files.length - 1);
            
            System.out.println(prefix + (isLast ? "└── " : "├── ") + file.getName() + 
                              (file.isDirectory() ? "/" : " (" + file.length() + " bytes)"));
            
            if (file.isDirectory()) {
                printDirectoryTree(file, prefix + (isLast ? "    " : "│   "));
            }
        }
    }
    
    /**
     * 递归删除目录
     */
    private static void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
